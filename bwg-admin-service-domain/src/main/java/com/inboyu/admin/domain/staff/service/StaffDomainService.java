package com.inboyu.admin.domain.staff.service;

import com.inboyu.admin.domain.staff.model.Staff;
import com.inboyu.admin.domain.staff.model.StaffId;
import com.inboyu.admin.domain.staff.model.UserId;
import com.inboyu.spring.cloud.starter.common.dto.Pagination;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年08月05日 11:35
 */
public interface StaffDomainService {

    /**
     * 生成员工id
     *
     * @return {@link StaffId }
     * <AUTHOR>
     * @date 2025/08/05 11:35:14
     */
    StaffId generateId();

    /**
     * 创建员工
     *
     * @param staff
     * @return {@link Staff }
     * <AUTHOR>
     * @date 2025/08/05 11:35:03
     */
    Staff createStaff(Staff staff);

    /**
     * 根据员工ID查询员工
     *
     * @param staffId
     * @return {@link Staff }
     * <AUTHOR> Dong
     * @date 2025/08/05 11:36:00
     */
    Staff findByStaffId(StaffId staffId);

    /**
     * 发送创建员工事件
     *
     * @param staff
     * <AUTHOR>
     * @date 2025/08/05 20:37:49
     */
    void sendCreateEvent(Staff staff);

    /**
     * 更新员工
     *
     * @param staff
     * @return {@link Staff }
     * <AUTHOR>
     * @date 2025/08/06 17:42:06
     */
    Staff updateStaff(Staff staff);

    /**
     * @description: 分页查询员工
     * @author: zhouxin
     * @date: 2025/8/5 19:34
     * @param: [pageNum, pageSize, keyword, status]
     * @return: com.inboyu.spring.cloud.starter.common.dto.Pagination<com.inboyu.admin.domain.staff.model.Staff>
     **/
    Pagination<Staff> pageByKeywordLike(Integer pageNum, Integer pageSize, String keyword, String status, List<Long> staffIds);

    /**
     * 发送员工更新事件
     *
     * @param userId
     * @param staff
     * <AUTHOR> Dong
     * @date 2025/08/06 17:43:45
     */
    void sendUpdateEvent(UserId userId, Staff staff);
}
