package com.inboyu.admin.domain.store.service;

import com.inboyu.admin.domain.dept.model.DeptId;
import com.inboyu.admin.domain.store.model.Store;
import com.inboyu.admin.domain.store.model.StoreId;
import com.inboyu.spring.cloud.starter.common.dto.Pagination;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年08月02日 10:56
 */
public interface StoreDomainService {

    /**
     * 生成门店id
     *
     * @return {@link StoreId }
     * <AUTHOR> Dong
     * @date 2025/08/02 14:37:14
     */
    StoreId generateId();

    /**
     * 创建门店
     *
     * @param store
     * @return {@link Store }
     * <AUTHOR> Dong
     * @date 2025/08/02 14:43:03
     */
    Store createStore(Store store);

    /**
     * 根据门店ID查询门店
     *
     * @param storeId
     * @return {@link Store }
     * <AUTHOR> Dong
     * @date 2025/08/02 16:00
     */
    Store findByStoreId(StoreId storeId);

    /**
     * 更新门店
     *
     * @param store
     * <AUTHOR>
     * @date 2025/08/02 16:00
     */
    void updateStore(Store store);

    /**
     * @param pageNum  当前页
     * @param pageSize 每页条数
     * @param keywords 门店名称关键词(支持模糊)
     * @param deptIds  组织ID列表，支持查询多个组织下的门店
     * @return {@link Pagination }<{@link Store }>
     * @return {@link Pagination }<{@link Store }>
     * <AUTHOR> Dong
     * @date 2025/08/04 11:40:33
     */
    Pagination<Store> pageStores(Integer pageNum, Integer pageSize, String keywords, List<DeptId> deptIds);

    /**
     * @param deptIds
     * @return {@link Integer }
     * <AUTHOR> Dong
     * @date 2025/08/04 17:56:00
     */
    Integer countByDeptIds(List<DeptId> deptIds);

    /**
     * 根据门店ID列表批量查询门店
     *
     * @param storeIds 门店ID列表
     * @return {@link List }<{@link Store }>
     * <AUTHOR> Dong
     * @date 2025/08/05 12:00:00
     */
    List<Store> findByStoreIds(List<StoreId> storeIds);

    /**
     * 获取所有门店
     * @return
     */
    List<Store> getAll();
}
