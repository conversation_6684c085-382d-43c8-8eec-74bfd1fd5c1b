package com.inboyu.admin.domain.dict.model;

import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025年08月04日 10:20
 */
@Getter
@Builder
public class Dict {

    /**
     * 字典项
     */
    private final DictCode code;

    /**
     * 字典编码
     **/
    private final DictCode type;

    /**
     * 字典项值
     **/
    private String title;

    /**
     * 字典图标
     **/
    private String icon;

    /**
     * 字典备注
     **/
    private String remark;

    /**
     * 排序
     **/
    private Integer index;

    public static Dict create(DictCode code, DictCode type, String title, String icon, String remark, Integer index) {
        return Dict.builder()
                .code(code)
                .type(type)
                .title(title)
                .icon(icon)
                .remark(remark)
                .index(index)
                .build();
    }
}
