package com.inboyu.admin.domain.dept.service;

import com.inboyu.admin.domain.dept.model.Dept;
import com.inboyu.admin.domain.dept.model.DeptCreationResult;
import com.inboyu.admin.domain.dept.model.DeptId;
import com.inboyu.admin.domain.dept.model.DeptUpdateResult;

import java.util.List;

/**
 * <AUTHOR> Dong
 * @date 2025年07月29日 14:25
 */
public interface DeptDomainService {

    /**
     * 创建组织
     *
     * @param parentDeptId
     * @param title
     * @return {@link Dept }
     * <AUTHOR> Dong
     * @date 2025/07/30 11:09:13
     */
    DeptCreationResult createDept(DeptId parentDeptId, String title);

    /**
     * 更新组织
     *
     * @param deptId
     * @param title
     * @return {@link DeptUpdateResult }
     * <AUTHOR> Dong
     * @date 2025/07/31 09:30:00
     */
    DeptUpdateResult updateDept(DeptId deptId, String title);

    /**
     * 查询所有组织
     *
     * @return {@link List<Dept> }
     * <AUTHOR>
     * @date 2025/07/30 14:30:00
     */
    List<Dept> findAllDept();

    /**
     * 根据部门id查询
     *
     * @param deptId
     * @return {@link Dept }
     * <AUTHOR> Dong
     * @date 2025/08/02 15:09:11
     */
    Dept findByDeptId(DeptId deptId);

    /**
     * 查询所有子组织
     *
     * @param deptId
     * @return {@link List }<{@link Dept }>
     * <AUTHOR> Dong
     * @date 2025/08/04 15:53:43
     */
    List<Dept> findAllChildDept(DeptId deptId);

    /**
     * 删除组织
     *
     * @param deptId
     * <AUTHOR> Dong
     * @date 2025/08/04 15:53:43
     */
    void deleteDept(DeptId deptId);

    /**
     * 根据部门ID列表批量查询部门
     *
     * @param deptIds 部门ID列表
     * @return {@link List }<{@link Dept }>
     * <AUTHOR> Dong
     * @date 2025/08/05 12:00:00
     */
    List<Dept> findByDeptIds(List<DeptId> deptIds);

    /**
     * 批量删除组织
     *
     * @param deptIds 组织ID列表
     * <AUTHOR> Dong
     * @date 2025/08/05 12:00:00
     */
    void deleteDepts(List<DeptId> deptIds);
}
