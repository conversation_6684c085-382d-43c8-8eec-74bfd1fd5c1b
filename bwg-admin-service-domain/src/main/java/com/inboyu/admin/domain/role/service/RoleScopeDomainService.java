package com.inboyu.admin.domain.role.service;

import com.inboyu.admin.domain.role.model.RoleId;
import com.inboyu.admin.domain.role.model.RolePermission;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025年07月31日 14:16
 */
public interface RoleScopeDomainService {
    /**
     * 批量保存角色权限
     *
     * @param rolePermissions
     * <AUTHOR> Dong
     * @date 2025/07/31 16:10:14
     */
    void saveAll(List<RolePermission> rolePermissions);

    /**
     * 根据角色id查询权限列表
     *
     * @param roleId
     * @return {@link List }<{@link RolePermission }>
     * <AUTHOR>
     * @date 2025/07/31 17:08:08
     */
    List<RolePermission> findByRoleId(RoleId roleId);

    /**
     * 根据角色id删除角色权限
     *
     * @param id
     * <AUTHOR> Dong
     * @date 2025/07/31 19:45:16
     */
    void deleteByRoleId(RoleId id);

    /**
     * 查询角色权限并分组
     *
     * @param roleIds
     * @return {@link Map }<{@link RoleId }, {@link List }<{@link RolePermission }>>
     * <AUTHOR>
     * @date 2025/07/31 21:17:03
     */
    Map<RoleId, List<RolePermission>> findAngGroupByRoleIds(List<RoleId> roleIds);

    /**
     * 查询角色权限
     *
     * @param roleIds
     * @return {@link List }<{@link RolePermission }>
     * <AUTHOR> Dong
     * @date 2025/08/06 10:46:01
     */
    List<RolePermission> findByRoleIds(List<RoleId> roleIds);
}
