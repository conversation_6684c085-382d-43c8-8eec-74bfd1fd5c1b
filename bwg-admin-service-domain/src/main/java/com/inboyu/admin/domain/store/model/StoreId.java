package com.inboyu.admin.domain.store.model;

import lombok.Value;

/**
 * 门店ID值对象
 *
 * <AUTHOR>
 * @date 2025年07月30日 15:28
 */
@Value
public class StoreId {
    Long value;

    public StoreId(Long value) {
        if (null == value) {
            throw new NullPointerException("门店ID不能为空");
        }
        this.value = value;
    }
    public static StoreId of(Long value) {
        return new StoreId(value);
    }

    public static StoreId of(String value) {
        return new StoreId(Long.valueOf(value));
    }
}
