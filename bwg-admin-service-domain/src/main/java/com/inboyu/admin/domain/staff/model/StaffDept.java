package com.inboyu.admin.domain.staff.model;

import com.inboyu.admin.domain.dept.model.DeptId;
import com.inboyu.admin.domain.role.model.RoleId;
import com.inboyu.admin.domain.store.model.StoreId;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> Dong
 * @date 2025年08月04日 20:07
 */
@Data
@Builder
public class StaffDept {
    /**
     * 员工ID
     */
    private final StaffId staffId;
    /**
     * 组织ID
     */
    private DeptId deptId;
    /**
     * 是否包含组织下所有门店
     */
    private Boolean includeAll;
    /**
     * 关联门店ID
     */
    private List<StoreId> storeIds = new ArrayList<>();
    /**
     * 关联角色ID
     */
    private List<RoleId> roleIds = new ArrayList<>();
    /**
     * 过期时间
     */
    private LocalDateTime expireTime;
}
