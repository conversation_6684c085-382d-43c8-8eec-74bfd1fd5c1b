package com.inboyu.admin.domain.dept.repository;

import com.inboyu.admin.domain.dept.model.Dept;
import com.inboyu.admin.domain.dept.model.DeptId;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年07月29日 14:23
 */
public interface DeptRepository {

    /**
     * 生成部门ID
     *
     * @return DeptId
     * <AUTHOR>
     * @date 2025/07/30 10:43:47
     */
    DeptId generateId();

    /**
     * 保存部门
     *
     * @param dept
     * @return {@link Dept }
     * <AUTHOR> Dong
     * @date 2025/07/29 15:26:53
     */
    Dept saveDept(Dept dept);

    /**
     * 根据组织id查询组织
     *
     * @param deptId
     * @return {@link Dept }
     * <AUTHOR> Dong
     * @date 2025/07/29 15:26:49
     */
    Dept findByDeptId(DeptId deptId);

    /**
     * 根据上级组织id+组织名称查询组织数量
     *
     * @param deptId
     * @param title
     * @return int
     * <AUTHOR>
     * @date 2025/07/29 20:30:14
     */
    int countByParentDeptIdAndTitle(DeptId deptId, String title);

    /**
     * 查询所有组织
     *
     * @return {@link List<Dept> }
     * <AUTHOR> Dong
     * @date 2025/07/30 14:30:00
     */
    List<Dept> findAllDept();

    /**
     * 更新部门
     *
     * @param dept
     * @return {@link Dept }
     * <AUTHOR> Dong
     * @date 2025/07/31 09:35:00
     */
    void updateDeptTitle(Dept dept);

    /**
     * 根据上级组织id+组织名称查询组织数量（排除指定组织）
     *
     * @param parentDeptId
     * @param title
     * @param deptId
     * @return int
     * <AUTHOR> Dong
     * @date 2025/07/31 09:35:00
     */
    int countByParentDeptIdAndTitleAndDeptIdNot(DeptId parentDeptId, String title, DeptId deptId);

    /**
     * 查询所有子组织
     *
     * @param deptId
     * @return {@link List }<{@link Dept }>
     * <AUTHOR> Dong
     * @date 2025/08/04 15:55:23
     */
    List<Dept> findAllChildDept(DeptId deptId);

    /**
     * 删除部门
     *
     * @param deptId
     * @return int
     * <AUTHOR> Dong
     * @date 2025/08/04 15:55:23
     */
    int deleteDept(DeptId deptId);

    /**
     * 根据部门ID列表批量查询部门
     *
     * @param deptIds 部门ID列表
     * @return {@link List }<{@link Dept }>
     * <AUTHOR> Dong
     * @date 2025/08/05 12:00:00
     */
    List<Dept> findByDeptIds(List<DeptId> deptIds);

    /**
     * 批量删除部门
     *
     * @param deptIds 部门ID列表
     * @return int
     * <AUTHOR> Dong
     * @date 2025/08/05 12:00:00
     */
    int deleteDepts(List<DeptId> deptIds);
}
