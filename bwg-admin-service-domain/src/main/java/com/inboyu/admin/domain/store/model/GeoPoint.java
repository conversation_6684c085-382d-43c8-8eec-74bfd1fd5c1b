package com.inboyu.admin.domain.store.model;

import lombok.Value;

import java.math.BigDecimal;

/**
 * 坐标值对象
 *
 * <AUTHOR> Dong
 * @date 2025年08月02日 10:45
 */
@Value
public class GeoPoint {
    /**
     * 经度
     */
    BigDecimal longitude;
    /**
     * 纬度
     */
    BigDecimal latitude;

    public GeoPoint(BigDecimal longitude, BigDecimal latitude) {
        if (longitude == null || latitude == null ||
                latitude.compareTo(BigDecimal.valueOf(-90)) < 0 || latitude.compareTo(BigDecimal.valueOf(90)) > 0
                || longitude.compareTo(BigDecimal.valueOf(-180)) < 0 || longitude.compareTo(BigDecimal.valueOf(180)) > 0) {
            throw new IllegalArgumentException("经纬度非法");
        }
        this.longitude = longitude;
        this.latitude = latitude;
    }

    public static GeoPoint of(BigDecimal longitude, BigDecimal latitude) {
        return new GeoPoint(longitude, latitude);
    }
}
