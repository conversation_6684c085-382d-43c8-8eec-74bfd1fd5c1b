package com.inboyu.admin.domain.dict.model;


import lombok.Value;

/**
 * <AUTHOR>
 * @date 2025年08月04日 10:20
 */
@Value
public class DictCode {

    String value;

    public DictCode(String value) {
        if (value == null) {
            throw new IllegalArgumentException("字典项非法");
        }
        this.value = value;
    }

    public static DictCode of(String value) {
        return new DictCode(value);
    }
}
