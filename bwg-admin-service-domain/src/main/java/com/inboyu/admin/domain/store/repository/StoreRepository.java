package com.inboyu.admin.domain.store.repository;

import com.inboyu.admin.domain.dept.model.DeptId;
import com.inboyu.admin.domain.store.model.Store;
import com.inboyu.admin.domain.store.model.StoreId;
import com.inboyu.spring.cloud.starter.common.dto.Pagination;

import java.util.List;

/**
 * <AUTHOR> Dong
 * @date 2025年08月02日 09:44
 */
public interface StoreRepository {

    /**
     * 生成门店id
     *
     * @return {@link StoreId }
     * <AUTHOR> Dong
     * @date 2025/08/02 14:38:46
     */
    StoreId generateId();

    /**
     * 根据门店名称统计门店数量
     *
     * @param title
     * @return int
     * <AUTHOR> Dong
     * @date 2025/08/02 14:49:45
     */
    int countByTitle(String title);

    /**
     * 根据门店名称和门店ID统计门店数量（用于更新时排除自己）
     *
     * @param title
     * @param storeId
     * @return int
     * <AUTHOR> Dong
     * @date 2025/08/02 16:00
     */
    int countByTitleAndStoreIdNot(String title, StoreId storeId);

    /**
     * 保存门店
     *
     * @param store
     * @return {@link Store }
     * <AUTHOR> Dong
     * @date 2025/08/02 14:49:59
     */
    Store save(Store store);

    /**
     * 根据门店ID查询门店
     *
     * @param storeId
     * @return {@link Store }
     * <AUTHOR> Dong
     * @date 2025/08/02 16:00
     */
    Store findByStoreId(StoreId storeId);

    /**
     * 更新门店
     *
     * @param store
     * <AUTHOR> Dong
     * @date 2025/08/02 16:00
     */
    void update(Store store);

//    /**
//     * 发送门店创建消息
//     *
//     * @param store
//     * <AUTHOR> Dong
//     * @date 2025/08/02 15:51:34
//     */
//    void sendCreateEvent(Store store);

//    /**
//     * 发送门店更新消息
//     *
//     * @param store
//     * <AUTHOR> Dong
//     * @date 2025/08/02 16:30
//     */
//    void sendUpdateEvent(Store store);

    /**
     * 分页查询门店
     *
     * @param pageNum  当前页
     * @param pageSize 每页条数
     * @param keywords 门店名称关键词(支持模糊)
     * @param deptIds  组织ID列表，支持查询多个组织下的门店
     * @return {@link com.inboyu.spring.cloud.starter.common.dto.Pagination }<{@link Store }>
     * <AUTHOR> Dong
     * @date 2025/08/04 11:40:33
     */
    Pagination<Store> pageStores(Integer pageNum, Integer pageSize, String keywords, List<DeptId> deptIds);

    /**
     * 批量查询组织下门店数
     *
     * @param deptIds
     * @return {@link Integer }
     * <AUTHOR> Dong
     * @date 2025/08/04 17:57:39
     */
    Integer countByDeptIds(List<DeptId> deptIds);

    /**
     * 根据门店ID列表批量查询门店
     *
     * @param storeIds 门店ID列表
     * @return {@link List }<{@link Store }>
     * <AUTHOR> Dong
     * @date 2025/08/05 12:00:00
     */
    List<Store> findByStoreIds(List<StoreId> storeIds);

    /**
     * 根据关联组织查询门店
     * @param deptIds
     * @return
     */
    List<Store> findByDeptIds(List<DeptId> deptIds);

    List<Store> findAll();
}
