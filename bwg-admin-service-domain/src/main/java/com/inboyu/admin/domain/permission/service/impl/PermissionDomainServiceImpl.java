package com.inboyu.admin.domain.permission.service.impl;

import com.inboyu.admin.domain.permission.model.MenuPermission;
import com.inboyu.admin.domain.permission.repository.PermissionRepository;
import com.inboyu.admin.domain.permission.service.PermissionDomainService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> Dong
 * @date 2025年08月05日 20:58
 */
@Service
public class PermissionDomainServiceImpl implements PermissionDomainService {
    @Autowired
    private PermissionRepository permissionRepository;
    @Override
    public MenuPermission getPermissionsTree() {
        return permissionRepository.getPermissionsTree();
    }
}
