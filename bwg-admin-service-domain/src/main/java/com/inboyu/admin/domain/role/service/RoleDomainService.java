package com.inboyu.admin.domain.role.service;

import com.inboyu.admin.domain.role.model.Role;
import com.inboyu.admin.domain.role.model.RoleId;

import java.util.List;

public interface RoleDomainService {

    /**
     * 生成角色id
     *
     * @return {@link RoleId }
     * <AUTHOR>
     * @date 2025/07/31 15:14:24
     */
    RoleId generateId();

    Role queryRoleInfo(RoleId roleId);

    /**
     * 创建角色
     *
     * @param role
     * @return {@link Role }
     * <AUTHOR>
     * @date 2025/07/31 14:34:54
     */
    Role createRole(Role role);

    /**
     * 更新角色
     *
     * @param role
     * @return {@link Role }
     * <AUTHOR>
     * @date 2025/07/31 14:34:54
     */
    void updateRole(Role role);

    /**
     * 查询角色详情
     *
     * @param roleId
     * @return {@link Role }
     * <AUTHOR>
     * @date 2025/07/31 16:58:38
     */
    Role findByRoleId(RoleId roleId);

    /**
     * 查询可见角色详情
     *
     * @param roleId
     * @return {@link Role }
     * <AUTHOR>
     * @date 2025/07/31 16:58:38
     */
    Role findByRoleIdAndEnabled(RoleId roleId, Boolean enabled);

    /**
     * 通过角色名称+状态查询角色列表(支持模糊)
     *
     * @param status
     * @param title
     * @return {@link List }<{@link Role }>
     * <AUTHOR> Dong
     * @date 2025/07/31 21:00:22
     */
    List<Role> findRolesByStatusAndTitleLike(String status, String title);

    /**
     * 查询所有角色
     *
     * @return {@link List }<{@link Role }>
     * <AUTHOR> Dong
     * @date 2025/08/01 10:14:39
     */
    List<Role> findAll();

    /**
     * 根据角色ID列表批量查询角色
     *
     * @param roleIds 角色ID列表
     * @return {@link List }<{@link Role }>
     * <AUTHOR> Dong
     * @date 2025/08/05 12:00:00
     */
    List<Role> findByRoleIds(List<RoleId> roleIds);
}
