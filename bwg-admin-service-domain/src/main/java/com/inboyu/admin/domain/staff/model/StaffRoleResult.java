package com.inboyu.admin.domain.staff.model;

import com.inboyu.admin.domain.dept.model.DeptId;
import com.inboyu.admin.domain.role.model.Role;
import com.inboyu.admin.domain.role.model.RoleId;
import com.inboyu.admin.domain.store.model.Store;
import com.inboyu.admin.domain.store.model.StoreId;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description: TODO
 * @Author: zhouxin
 * @Date: 2025-08-05
 */
@Data
@Builder
public class StaffRoleResult {

    /**
     * 员工ID
     */
    private final StaffId staffId;
    /**
     * 组织ID
     */
    private DeptId deptId;
    /**
     * 部门名称
     */
    private String title;
    /**
     * 是否包含组织下所有门店
     */
    private Boolean includeAll;
    /**
     * 关联门店信息
     */
    private List<Store> stores = new ArrayList<>();
    /**
     * 关联角色信息
     */
    private List<Role> roles = new ArrayList<>();
    /**
     * 过期时间
     */
    private LocalDateTime expireTime;
}
