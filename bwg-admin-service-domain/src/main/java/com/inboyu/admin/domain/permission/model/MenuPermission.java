package com.inboyu.admin.domain.permission.model;

import lombok.Data;

import java.util.List;

/**
 * 菜单权限结构DTO
 * 用于封装多级菜单及关联的权限信息
 */
@Data
public class MenuPermission {

    /**
     * 菜单列表（一级菜单）
     */
    private List<MenuDTO> menus;

    /**
     * 菜单DTO（支持三级嵌套）
     */
    @Data
    public static class MenuDTO {

        /**
         * 菜单编码
         */
        private String code;

        /**
         * 菜单名称
         */
        private String title;

        /**
         * 菜单图标
         */
        private String icon;

        /**
         * 菜单关联的权限集合
         */
        private List<PermissionDTO> permissions;

        /**
         * 子菜单（二级菜单）
         */
        private List<MenuDTO> children;
    }

    /**
     * 权限DTO
     */
    @Data
    public static class PermissionDTO {

        /**
         * 权限编码
         */
        private String code;

        /**
         * 权限名称
         */
        private String title;

        /**
         * 接口URL
         */
        private String url;
    }
}
