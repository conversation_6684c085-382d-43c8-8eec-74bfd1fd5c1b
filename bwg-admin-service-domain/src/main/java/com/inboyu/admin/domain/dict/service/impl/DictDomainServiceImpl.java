package com.inboyu.admin.domain.dict.service.impl;

import com.inboyu.admin.domain.dict.model.Dict;
import com.inboyu.admin.domain.dict.model.DictCode;
import com.inboyu.admin.domain.dict.model.DictCreationResult;
import com.inboyu.admin.domain.dict.model.DictUpdateResult;
import com.inboyu.admin.domain.dict.repository.DictRepository;
import com.inboyu.admin.domain.dict.service.DictDomainService;
import com.inboyu.admin.exception.ResponseCode;
import com.inboyu.spring.cloud.starter.common.exception.AppException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> Dong
 * @date 2025年08月04日 10:34
 */
@Service
public class DictDomainServiceImpl implements DictDomainService{

    @Autowired
    private DictRepository dictRepository;

    @Override
    public DictCode generateCode(String type) {
        return dictRepository.generateCode(type);
    }

    @Override
    public DictCreationResult createDict(Dict dict) {
        int count = dictRepository.countByTypeAndTitle(dict.getType().getValue(), dict.getTitle());
        if (count > 0) {
            throw new AppException(ResponseCode.DICT_UPDATE_DUPLICATE_TITLE);
        }
        Dict insert = dictRepository.insert(dict);
        return new DictCreationResult(insert);
    }

    @Override
    public void deleteDict(DictCode type, DictCode code) {
        Dict byTypeAndCode = dictRepository.findByTypeAndCode(type.getValue(), code.getValue());
        if (null == byTypeAndCode) {
            throw new AppException(ResponseCode.DICT_NOT_EXIST);
        }
        dictRepository.delete(type, code);
    }

    @Override
    public DictUpdateResult updateDict(Dict dict) {
        Dict byTypeAndCode = dictRepository.findByTypeAndCode(dict.getType().getValue(), dict.getCode().getValue());
        if (null == byTypeAndCode) {
            throw new AppException(ResponseCode.DICT_NOT_EXIST);
        }
        if (Objects.equals(dict.getTitle(), byTypeAndCode.getTitle())) {
            Dict update = dictRepository.update(dict);
            return new DictUpdateResult(update);
        }
        int count = dictRepository.countByTypeAndTitle(dict.getType().getValue(), dict.getTitle());
        if (count > 0) {
            throw new AppException(ResponseCode.DICT_UPDATE_DUPLICATE_TITLE);
        }
        Dict update = dictRepository.update(dict);
        return new DictUpdateResult(update);
    }

    @Override
    public List<Dict> queryDictListByType(DictCode type) {
        return dictRepository.queryDictListByType(type);
    }
}
