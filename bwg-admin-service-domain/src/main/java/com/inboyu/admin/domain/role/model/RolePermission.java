package com.inboyu.admin.domain.role.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 角色权限
 *
 * <AUTHOR>
 * @date 2025年07月30日 16:27
 */
@Getter
@AllArgsConstructor
public class RolePermission {
    /**
     * 角色ID
     */
    private final RoleId roleId;
    /**
     * 菜单编码
     */
    private String groupCode;
    /**
     * 权限
     */
    private final Permission permission;
    /**
     * 是否隐藏
     */
    private Boolean hidden;
    /**
     * 是否禁用
     */
    private Boolean disabled;
    /**
     * 创建角色权限
     *
     * @param roleId      角色ID
     * @param permission  权限
     * @param hidden      是否隐藏
     * @param disabled    是否禁用
     * @return 角色权限
     */
    public static RolePermission create(RoleId roleId, String groupCode, Permission permission, Boolean hidden, Boolean disabled) {
        return new RolePermission(roleId, groupCode, permission, hidden, disabled);
    }
}
