package com.inboyu.admin.domain.dict.service;

import com.inboyu.admin.domain.dict.model.Dict;
import com.inboyu.admin.domain.dict.model.DictCode;
import com.inboyu.admin.domain.dict.model.DictCreationResult;
import com.inboyu.admin.domain.dict.model.DictUpdateResult;

import java.util.List;

/**
 * <AUTHOR> Dong
 * @date 2025年08月04日 10:34
 */
public interface DictDomainService {

    /**
     * 创建字典code
     *
     * @param type
     * @return {@link DictCode }
     * <AUTHOR> Dong
     * @date 2025/08/04 14:34:54
     */
    DictCode generateCode(String type);

    /**
     * 创建字典
     *
     * @param dict
     * @return {@link DictCreationResult }
     * <AUTHOR> Dong
     * @date 2025/08/04 14:34:54
     */
    DictCreationResult createDict(Dict dict);

    /**
     * 根据字典编码删除字典
     *
     * @param type
     * @param code
     * @return void
     * <AUTHOR> Dong
     * @date 2025/08/04 14:34:54
     */
    void deleteDict(DictCode type, DictCode code);

    /**
     * 更新字典
     *
     * @param dict
     * @return {@link DictUpdateResult }
     * <AUTHOR> Dong
     * @date 2025/08/04 14:34:54
     */
    DictUpdateResult updateDict(Dict dict);

    /**
     * 根据字典编码查询字典
     *
     * @param type
     * @return {@link List<Dict> }
     * <AUTHOR> Dong
     * @date 2025/08/04 14:34:54
     */
    List<Dict> queryDictListByType(DictCode type);

}
