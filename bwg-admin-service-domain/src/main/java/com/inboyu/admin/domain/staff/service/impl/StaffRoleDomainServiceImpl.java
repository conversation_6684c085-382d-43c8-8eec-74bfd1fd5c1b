package com.inboyu.admin.domain.staff.service.impl;

import com.inboyu.admin.domain.dept.model.Dept;
import com.inboyu.admin.domain.dept.model.DeptId;
import com.inboyu.admin.domain.dept.repository.DeptRepository;
import com.inboyu.admin.domain.role.model.Role;
import com.inboyu.admin.domain.role.repository.RoleRepository;
import com.inboyu.admin.domain.staff.model.StaffDept;
import com.inboyu.admin.domain.staff.model.StaffId;
import com.inboyu.admin.domain.staff.model.StaffRoleResult;
import com.inboyu.admin.domain.staff.repository.StaffRoleRepository;
import com.inboyu.admin.domain.staff.service.StaffRoleDomainService;
import com.inboyu.admin.domain.store.model.Store;
import com.inboyu.admin.domain.store.repository.StoreRepository;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年08月05日 15:47
 */
@Service
public class StaffRoleDomainServiceImpl implements StaffRoleDomainService {
    @Autowired
    private StaffRoleRepository staffRoleRepository;

    @Autowired
    private DeptRepository deptRepository;

    @Autowired
    private StoreRepository storeRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Override
    public void saveAll(List<StaffDept> staffDeptList) {
        staffRoleRepository.saveAll(staffDeptList);
    }

    @Override
    public List<StaffRoleResult> listDeptStaff(StaffId staffId) {
        List<StaffDept> deptList = staffRoleRepository.queryByStaffId(staffId);
        return getStaffRoleResults(deptList);
    }

    /**
     * @description: 处理查询结果
     * @author: zhouxin
     * @date: 2025/8/5 19:56
     * @param: [deptList]
     * @return: java.util.List<com.inboyu.admin.domain.staff.model.StaffRoleResult>
     **/
    private List<StaffRoleResult> getStaffRoleResults(List<StaffDept> deptList) {
        if (CollectionUtils.isEmpty(deptList)) {
            return Collections.emptyList();
        }
        return deptList.stream().map(staffDept -> {
            List<Store> stores = storeRepository.findByStoreIds(staffDept.getStoreIds());
            if (CollectionUtils.isEmpty(stores)) {
                stores = Collections.emptyList();
            }
            List<Role> roles = roleRepository.findByRoleIds(staffDept.getRoleIds());
            if (CollectionUtils.isEmpty(roles)) {
                roles = Collections.emptyList();
            }
            Dept dept = deptRepository.findByDeptId(staffDept.getDeptId());
            if (null == dept) {
                return StaffRoleResult.builder().build();
            }
            return StaffRoleResult
                    .builder()
                    .staffId(staffDept.getStaffId())
                    .deptId(staffDept.getDeptId())
                    .title(dept.getTitle())
                    .includeAll(staffDept.getIncludeAll())
                    .stores(stores)
                    .roles(roles)
                    .expireTime(staffDept.getExpireTime())
                    .build();
        }).toList();
    }

    @Override
    public Integer countByDeptIds(List<DeptId> deptIds) {
        return staffRoleRepository.countByDeptIds(deptIds);
    }

    @Override
    public List<StaffDept> findByStaffId(StaffId staffId) {
        List<StaffDept> list = staffRoleRepository.queryByStaffId(staffId);
        list.stream().filter(it -> it.getIncludeAll()).peek(it -> {
            // 递归获取所有子组织
            List<Dept> depts = deptRepository.findAllChildDept(it.getDeptId());
            // 当前组织
            Dept dept = deptRepository.findByDeptId(it.getDeptId());
            // 当前及所有子组织
            depts.addFirst(dept);
            // 获取deptId列表
            List<DeptId> deptIds = depts.stream().map(d -> d.getId()).toList();
            // 查询门店id作为scope
            List<Store> stores = storeRepository.findByDeptIds(deptIds);
            // 完善门店ID
            it.setStoreIds(stores.stream().map(s -> s.getId()).toList());
        });
        return list;
    }

    @Override
    public void updateStaffRoles(StaffId staffId, List<StaffDept> staffDepts) {
        staffRoleRepository.deleteByStaffId(staffId);
        if (ObjectUtils.isNotEmpty(staffDepts)) {
            saveAll(staffDepts);
        }
    }

    @Override
    public List<StaffRoleResult> listByRoleIdsAndDeptIds(List<String> roleIds, List<Long> deptIds) {
        List<StaffDept> deptList = staffRoleRepository.listByRoleIdsAndDeptIds(roleIds, deptIds);
        if (CollectionUtils.isEmpty(deptList)) {
            return Collections.emptyList();
        }
        List<StaffRoleResult> staffRoleResults = getStaffRoleResults(deptList);
        if (CollectionUtils.isEmpty(staffRoleResults)) {
            return Collections.emptyList();
        }
        return staffRoleResults;
    }
}
