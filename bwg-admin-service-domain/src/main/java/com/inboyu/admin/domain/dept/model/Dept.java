package com.inboyu.admin.domain.dept.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;

/**
 * 组织实体
 *
 * <AUTHOR> Dong
 * @date 2025年07月29日 14:26
 */
@Getter
@Builder
@AllArgsConstructor
public class Dept {
    private final DeptId id;
    private final DeptId parentDeptId;
    private String title;

    // 更新方法
    public Dept updateTitle(String newTitle) {
        return Dept.builder()
                .id(this.id)
                .parentDeptId(this.parentDeptId)
                .title(newTitle)
                .build();
    }
}
