package com.inboyu.admin.domain.dept.model;

import lombok.Value;

import java.util.List;

/**
 * 组织ID值对象
 *
 * <AUTHOR> Dong
 * @date 2025年07月29日 14:28
 */
@Value
public class DeptId {

    Long value;

    public DeptId(Long value) {
        if (value == null) {
            throw new IllegalArgumentException("组织id非法");
        }
        this.value = value;
    }

    public static DeptId of(Long value) {
        return new DeptId(value);
    }

    public static List<Long> ofList(List<DeptId> deptIds) {
        return deptIds.stream().map(DeptId::getValue).toList();
    }
}
