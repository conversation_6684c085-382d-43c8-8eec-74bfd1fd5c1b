package com.inboyu.admin.domain.staff.service;

import com.inboyu.admin.domain.dept.model.DeptId;
import com.inboyu.admin.domain.staff.model.StaffDept;
import com.inboyu.admin.domain.staff.model.StaffId;
import com.inboyu.admin.domain.staff.model.StaffRoleResult;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年08月05日 15:46
 */
public interface StaffRoleDomainService {

    void saveAll(List<StaffDept> staffDeptList);

    List<StaffRoleResult> listDeptStaff(StaffId staffId);

    Integer countByDeptIds(List<DeptId> deptIds);

    List<StaffDept> findByStaffId(StaffId staffId);

    List<StaffRoleResult> listByRoleIdsAndDeptIds(List<String> roleIds, List<Long> deptIds);

    void updateStaffRoles(StaffId staffId, List<StaffDept> staffDepts);
}
