package com.inboyu.admin.domain.staff.model;

import lombok.Value;

/**
 * <AUTHOR>
 * @date 2025年08月05日 10:13
 */
@Value
public class PhoneNumber {
    String value;

    private PhoneNumber(String value) {
        if (value == null || value.length() != 11) {
            throw new IllegalArgumentException("手机号码格式不正确");
        }
        this.value = value;
    }

    public static PhoneNumber of(String value) {
        return new PhoneNumber(value);
    }
}
