package com.inboyu.admin.domain.staff.service.impl;

import com.inboyu.admin.domain.staff.model.Staff;
import com.inboyu.admin.domain.staff.model.StaffId;
import com.inboyu.admin.domain.staff.model.UserId;
import com.inboyu.admin.domain.staff.repository.StaffRepository;
import com.inboyu.admin.domain.staff.service.StaffDomainService;
import com.inboyu.admin.exception.ResponseCode;
import com.inboyu.spring.cloud.starter.common.dto.Pagination;
import com.inboyu.spring.cloud.starter.common.exception.AppException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年08月05日 11:37
 */
@Service
public class StaffDomainServiceImpl implements StaffDomainService {

    @Autowired
    private StaffRepository staffRepository;

    @Override
    public StaffId generateId() {
        return staffRepository.generateId();
    }

    @Override
    public Staff createStaff(Staff staff) {
        // 1.校验手机号是否重复
        int count = staffRepository.countByPhone(staff.getPhone().getValue());
        if (count > 0) {
            throw new AppException(ResponseCode.STAFF_DUPLICATE_PHONE);
        }
        // 2.查询userId
        UserId userId = staffRepository.findUserIdByPhone(staff.getPhone().getValue());
        staff.setUserId(userId);
        // 3.创建员工
        return staffRepository.saveStaff(staff);
    }

    @Override
    public Staff findByStaffId(StaffId staffId) {
        return staffRepository.findByStaffId(staffId);
    }

    /**
     * 更新员工
     *
     * @param staff
     * @return {@link Staff }
     * <AUTHOR> Dong
     * @date 2025/08/06 17:42:17
     */
    @Override
    public Staff updateStaff(Staff staff) {
        // 1.校验同级组织角色不重复（排除自己）
        if (staffRepository.countByPhoneAndStaffIdNot(staff.getPhone(), staff.getId()) > 0) {
            throw new AppException(ResponseCode.STAFF_UPDATE_DUPLICATE_PHONE);
        }
        // 2.查询userId
        UserId userId = staffRepository.findUserIdByPhone(staff.getPhone().getValue());
        staff.setUserId(userId);
        // 3.更新员工
        staffRepository.updateStaff(staff);
        return staff;
    }

    /**
     * 发送创建员工事件
     *
     * @param staff
     * <AUTHOR> Dong
     * @date 2025/08/05 20:37:49
     */
    @Override
    public void sendCreateEvent(Staff staff) {
        staffRepository.sendCreateEvent(staff);
    }

    @Override
    public Pagination<Staff> pageByKeywordLike(Integer pageNum, Integer pageSize, String keyword, String status, List<Long> staffIds) {
        return staffRepository.pageByKeywordLike(pageNum, pageSize, keyword, status, staffIds);
    }

    /**
     * 发送员工更新事件
     *
     * @param userId
     * @param staff
     * <AUTHOR> Dong
     * @date 2025/08/06 17:43:45
     */
    @Override
    public void sendUpdateEvent(UserId userId, Staff staff) {
        staffRepository.sendUpdateEvent(userId, staff);
    }
}
