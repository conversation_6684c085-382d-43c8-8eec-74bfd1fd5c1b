package com.inboyu.admin.domain.dict.repository;

import com.inboyu.admin.domain.dict.model.Dict;
import com.inboyu.admin.domain.dict.model.DictCode;

import java.util.List;


/**
 * <AUTHOR> Dong
 * @date 2025年08月04日 10:20
 */
public interface DictRepository {

    /**
     * 创建字典code
     *
     * @param type
     * @return {@link DictCode }
     * <AUTHOR> Dong
     * @date 2025/08/04 14:34:54
     */
    DictCode generateCode(String type);

    /**
     * 创建字典
     *
     * @param dict
     * @return {@link Dict }
     * <AUTHOR> Dong
     * @date 2025/08/04 14:34:54
     */
    Dict insert(Dict dict);

    /**
     * 根据字典编码删除字典
     *
     * @param type
     * @param code
     * @return void
     * <AUTHOR> Dong
     * @date 2025/08/04 14:34:54
     */
    void delete(DictCode type, DictCode code);

    /**
     * 更新字典
     *
     * @param dict
     * @return {@link Dict }
     * <AUTHOR> Dong
     * @date 2025/08/04 14:34:54
     */
    Dict update(Dict dict);

    /**
     * 根据字典项类型查询字典列表
     *
     * @param type
     * @return {@link List<Dict> }
     * <AUTHOR> Dong
     * @date 2025/08/04 14:34:54
     */
    List<Dict> queryDictListByType(DictCode type);

    /**
     * 通过字典名称统计字典项数量
     *
     * @param type
     * @param title
     * @return {@link Integer }
     * <AUTHOR> Dong
     * @date 2025/08/04 14:34:54
     */
    int countByTypeAndTitle(String type, String title);

    /**
     * 通过字典编码查询字典项
     *
     * @param type
     * @param code
     * @return {@link Dict }
     * <AUTHOR> Dong
     * @date 2025/08/04 15:00:54
     */
    Dict findByTypeAndCode(String type, String code);
}
