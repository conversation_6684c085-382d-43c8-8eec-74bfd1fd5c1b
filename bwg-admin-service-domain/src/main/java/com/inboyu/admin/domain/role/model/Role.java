package com.inboyu.admin.domain.role.model;

import lombok.Builder;
import lombok.Getter;

/**
 * 角色实体
 *
 * <AUTHOR> Dong
 * @date 2025/07/30 16:32:37
 */
@Getter
@Builder
public class Role {
    /**
     * 角色ID
     */
    private final RoleId id;
    /**
     * 角色名称
     */
    private String title;
    /**
     * 角色描述
     */
    private String remark;
    /**
     * 角色状态
     */
    private RoleStatus status;

    /**
     * 是否可见
     */
    private Boolean enabled;

    public static Role create(RoleId id, String title, String remark, RoleStatus status, Boolean enabled) {
        return Role.builder()
                .id(id)
                .title(title)
                .remark(remark)
                .status(status)
                .enabled(enabled)
                .build();
    }


}
