package com.inboyu.admin.domain.role.repository;

import com.inboyu.admin.domain.role.model.RoleId;
import com.inboyu.admin.domain.role.model.RolePermission;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年07月31日 16:11
 */
public interface RoleScopeRepository {
    /**
     * 批量保存角色权限
     *
     * @param rolePermissions
     * <AUTHOR>
     * @date 2025/07/31 16:14:34
     */
    void saveAll(List<RolePermission> rolePermissions);

    /**
     * 根据角色id查询权限列表
     *
     * @param roleId
     * @return {@link List }<{@link RolePermission }>
     * <AUTHOR>
     * @date 2025/07/31 17:09:04
     */
    List<RolePermission> findByRoleId(RoleId roleId);

    /**
     * 根据角色id删除角色权限
     *
     * @param id
     * <AUTHOR>
     * @date 2025/07/31 19:45:55
     */
    void deleteByRoleId(RoleId id);

    /**
     * 通过角色id查询角色权限
     *
     * @param roleIds
     * @return {@link List }<{@link RolePermission }>
     * <AUTHOR>
     * @date 2025/08/01 09:51:46
     */
    List<RolePermission> findByRoleIds(List<RoleId> roleIds);
}
