package com.inboyu.admin.domain.store.service.impl;

import com.inboyu.admin.domain.dept.model.DeptId;
import com.inboyu.admin.domain.store.model.Store;
import com.inboyu.admin.domain.store.model.StoreId;
import com.inboyu.admin.domain.store.repository.StoreRepository;
import com.inboyu.admin.domain.store.service.StoreDomainService;
import com.inboyu.admin.exception.ResponseCode;
import com.inboyu.spring.cloud.starter.common.dto.Pagination;
import com.inboyu.spring.cloud.starter.common.exception.AppException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> Dong
 * @date 2025年08月02日 10:57
 */
@Service
public class StoreDomainServiceImpl implements StoreDomainService {
    @Autowired
    private StoreRepository storeRepository;

    @Override
    public Store createStore(Store store) {
        // 1.校验门店名称是否重复
        int count = storeRepository.countByTitle(store.getTitle());
        if (count > 0) {
            throw new AppException(ResponseCode.STORE_DUPLICATE_TITLE);
        }
        // 2.创建门店
        Store saveStore = storeRepository.save(store);
        // 3.发送创建门店事件
//        storeRepository.sendCreateEvent(saveStore);
        return saveStore;
    }

    /**
     * 生成门店id
     *
     * @return {@link StoreId }
     * <AUTHOR> Dong
     * @date 2025/08/02 14:37:14
     */
    @Override
    public StoreId generateId() {
        return storeRepository.generateId();
    }

    /**
     * 根据门店ID查询门店
     *
     * @param storeId
     * @return {@link Store }
     * <AUTHOR> Dong
     * @date 2025/08/02 16:00
     */
    @Override
    public Store findByStoreId(StoreId storeId) {
        return storeRepository.findByStoreId(storeId);
    }

    /**
     * 更新门店
     *
     * @param store
     * <AUTHOR> Dong
     * @date 2025/08/02 16:00
     */
    @Override
    public void updateStore(Store store) {
        // 1.校验门店名称是否重复（排除自己）
        int count = storeRepository.countByTitleAndStoreIdNot(store.getTitle(), store.getId());
        if (count > 0) {
            throw new AppException(ResponseCode.STORE_DUPLICATE_TITLE);
        }
        // 3.更新门店
        storeRepository.update(store);
        // 4.发送更新门店事件
//        storeRepository.sendUpdateEvent(store);
    }

    /**
     * 分页查询门店
     *
     * @param pageNum  当前页
     * @param pageSize 每页条数
     * @param keywords 门店名称关键词(支持模糊)
     * @param deptIds  组织ID列表，支持查询多个组织下的门店
     * @return {@link Pagination }<{@link Store }>
     * <AUTHOR> Dong
     * @date 2025/08/04 11:40:33
     */
    public Pagination<Store> pageStores(Integer pageNum, Integer pageSize, String keywords, List<DeptId> deptIds) {
        return storeRepository.pageStores(pageNum, pageSize, keywords, deptIds);
    }

    /**
     * 批量查询组织下门店数
     *
     * @param deptIds
     * @return {@link Integer }
     * <AUTHOR> Dong
     * @date 2025/08/04 17:56:00
     */
    @Override
    public Integer countByDeptIds(List<DeptId> deptIds) {
        return storeRepository.countByDeptIds(deptIds);
    }

    /**
     * 根据门店ID列表批量查询门店
     *
     * @param storeIds 门店ID列表
     * @return {@link List }<{@link Store }>
     * <AUTHOR> Dong
     * @date 2025/08/05 12:00:00
     */
    @Override
    public List<Store> findByStoreIds(List<StoreId> storeIds) {
        return storeRepository.findByStoreIds(storeIds);
    }

    @Override
    public List<Store> getAll() {
        return storeRepository.findAll();
    }
}
