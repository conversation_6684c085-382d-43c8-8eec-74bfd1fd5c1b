package com.inboyu.admin.domain.staff.model;

import lombok.Builder;
import lombok.Value;

/**
 * <AUTHOR>
 * @date 2025年08月04日 20:06
 */
@Value
@Builder
public class StaffStatus {
    /**
     * 编码
     */
    String code;
    /**
     * 值
     */
    String value;

    public static StaffStatus create(String code, String value) {
        return StaffStatus.builder().code(code).value(value).build();
    }
}
