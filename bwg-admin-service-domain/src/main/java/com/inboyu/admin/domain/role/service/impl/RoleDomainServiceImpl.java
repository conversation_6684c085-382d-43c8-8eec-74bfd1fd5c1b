package com.inboyu.admin.domain.role.service.impl;

import com.inboyu.admin.domain.role.model.Role;
import com.inboyu.admin.domain.role.model.RoleId;
import com.inboyu.admin.domain.role.repository.RoleRepository;
import com.inboyu.admin.domain.role.service.RoleDomainService;
import com.inboyu.admin.exception.ResponseCode;
import com.inboyu.spring.cloud.starter.common.exception.AppException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 角色领域服务实现
 *
 * <AUTHOR> Dong
 * @date 2025/07/31 14:17:26
 */
@Service
public class RoleDomainServiceImpl implements RoleDomainService {

    @Autowired
    private RoleRepository roleRepository;


    /**
     * 生成角色id
     *
     * @return {@link RoleId }
     * <AUTHOR> Dong
     * @date 2025/07/31 15:14:24
     */
    @Override
    public RoleId generateId() {
        return roleRepository.generateId();
    }

    @Override
    public Role queryRoleInfo(RoleId roleId) {
        return roleRepository.findByRoleId(roleId);
    }

    /**
     * 创建角色
     *
     * @param role
     * @return {@link Role }
     * <AUTHOR> Dong
     * @date 2025/07/31 14:34:54
     */
    @Override
    public Role createRole(Role role) {
        // 1.校验角色名称是否重复
        int count = roleRepository.countByTitle(role.getTitle());
        if (count > 0) {
            throw new AppException(ResponseCode.ROLE_DUPLICATE_TITLE);
        }
        // 2.创建角色
        return roleRepository.save(role);
    }

    /**
     * 更新角色
     *
     * @param role
     * @return {@link Role }
     * <AUTHOR> Dong
     * @date 2025/07/31 14:34:54
     */
    @Override
    public void updateRole(Role role) {
        // 1. 校验同级组织角色不重复（排除自己）
        if (roleRepository.countByTitleAndRoleIdNot(role.getTitle(), role.getId()) > 0) {
            throw new AppException(ResponseCode.ROLE_UPDATE_DUPLICATE_TITLE);
        }
        // 2. 更新角色
        roleRepository.updateRole(role);
    }

    /**
     * 查询角色详情
     *
     * @param roleId
     * @return {@link Role }
     * <AUTHOR> Dong
     * @date 2025/07/31 16:58:38
     */
    @Override
    public Role findByRoleId(RoleId roleId) {
        return roleRepository.findByRoleId(roleId);
    }

    /**
     * 查询可见角色详情
     *
     * @param roleId
     * @param enabled
     * @return {@link Role }
     * <AUTHOR> Dong
     * @date 2025/07/31 16:58:38
     */
    @Override
    public Role findByRoleIdAndEnabled(RoleId roleId, Boolean enabled) {
        return roleRepository.findByRoleIdAndEnabled(roleId, enabled);
    }

    /**
     * 通过角色名称查询角色列表(支持模糊)
     *
     * @param title
     * @param status
     * @return {@link List }<{@link Role }>
     * <AUTHOR> Dong
     * @date 2025/07/31 21:00:22
     */
    @Override
    public List<Role> findRolesByStatusAndTitleLike(String status, String title) {
        return roleRepository.findRolesByStatusAndTitleLike(status, title);
    }

    /**
     * 查询所有角色
     *
     * @return {@link List }<{@link Role }>
     * <AUTHOR> Dong
     * @date 2025/08/01 10:14:39
     */
    @Override
    public List<Role> findAll() {
        return roleRepository.findAll();
    }

    /**
     * 根据角色ID列表批量查询角色
     *
     * @param roleIds 角色ID列表
     * @return {@link List }<{@link Role }>
     * <AUTHOR> Dong
     * @date 2025/08/05 12:00:00
     */
    @Override
    public List<Role> findByRoleIds(List<RoleId> roleIds) {
        return roleRepository.findByRoleIds(roleIds);
    }
}
