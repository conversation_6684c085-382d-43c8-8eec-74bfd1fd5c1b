package com.inboyu.admin.domain.staff.repository;

import com.inboyu.admin.domain.staff.model.PhoneNumber;
import com.inboyu.admin.domain.staff.model.Staff;
import com.inboyu.admin.domain.staff.model.StaffId;
import com.inboyu.admin.domain.staff.model.UserId;
import com.inboyu.spring.cloud.starter.common.dto.Pagination;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2025年08月05日 11:30
 */
public interface StaffRepository {

    /**
     * 生成员工ID
     *
     * @return StaffId
     * <AUTHOR>
     * @date 2025/08/05 11:30:47
     */
    StaffId generateId();

    /**
     * 保存员工
     *
     * @param staff
     * @return {@link Staff }
     * <AUTHOR>
     * @date 2025/08/05 11:30:53
     */
    Staff saveStaff(Staff staff);

    /**
     * 根据员工id查询员工
     *
     * @param staffId
     * @return {@link Staff }
     * <AUTHOR>
     * @date 2025/08/05 11:31:49
     */
    Staff findByStaffId(StaffId staffId);

    /**
     * 根据手机号查询员工数量
     *
     * @param phone
     * @return int
     * <AUTHOR>
     * @date 2025/08/05 11:32:14
     */
    int countByPhone(String phone);

    /**
     * 根据手机号查询userId
     *
     * @param phone
     * @return {@link UserId }
     * <AUTHOR> Dong
     * @date 2025/08/05 15:33:15
     */
    UserId findUserIdByPhone(String phone);

    /**
     * 发送员工创建事件
     *
     * @param staff
     * <AUTHOR> Dong
     * @date 2025/08/05 16:54:19
     */
    void sendCreateEvent(Staff staff);

    int countByPhoneAndStaffIdNot(PhoneNumber phone, StaffId id);

    void updateStaff(Staff staff);

    /**
     * @description: 分页查询员工
     * @author: zhouxin
     * @date: 2025/8/5 19:34
     * @param: [pageNum, pageSize, keyword, status]
     * @return: com.inboyu.spring.cloud.starter.common.dto.Pagination<com.inboyu.admin.domain.staff.model.Staff>
     **/
    Pagination<Staff> pageByKeywordLike(Integer pageNum, Integer pageSize, String keyword, String status, List<Long> staffIds);

    /**
     * 发送员工更新事件
     *
     * @param userId
     * @param staff
     * <AUTHOR> Dong
     * @date 2025/08/06 17:47:29
     */
    void sendUpdateEvent(UserId userId, Staff staff);
}
