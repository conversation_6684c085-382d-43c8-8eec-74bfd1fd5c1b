package com.inboyu.admin.domain.staff.model;

import lombok.Value;

/**
 * <AUTHOR>
 * @date 2025年08月05日 10:12
 */
@Value
public class UserId {
    Long value;

    public UserId(Long value) {
        if (null == value) {
            throw new IllegalArgumentException("用户id非法");
        }
        this.value = value;
    }

    public static UserId of(Long value) {
        return new UserId(value);
    }
}
