package com.inboyu.admin.domain.role.repository;

import com.inboyu.admin.domain.role.model.Role;
import com.inboyu.admin.domain.role.model.RoleId;

import java.util.List;

public interface RoleRepository {

    Role findByRoleId(RoleId roleId);

    Role findByRoleIdAndEnabled(RoleId roleId, Boolean enabled);

    /**
     * 通过角色名称统计角色数量
     *
     * @param title
     * @return {@link Role }
     * <AUTHOR>
     * @date 2025/07/31 14:59:39
     */
    int countByTitle(String title);

    /**
     * 生成角色id
     *
     * @return {@link RoleId }
     * <AUTHOR>
     * @date 2025/07/31 15:11:06
     */
    RoleId generateId();

    /**
     * 保存角色
     *
     * @param role
     * @return {@link Role }
     * <AUTHOR>
     * @date 2025/07/31 15:16:46
     */
    Role save(Role role);

    /**
     * 根据角色名称查询角色数量（排除指定角色）
     *
     * @param title
     * @param id
     * @return int
     * <AUTHOR>
     * @date 2025/07/31 20:04:43
     */
    int countByTitleAndRoleIdNot(String title, RoleId id);

    /**
     * 更新角色
     *
     * @param role
     * <AUTHOR>
     * @date 2025/07/31 20:14:16
     */
    void updateRole(Role role);

    /**
     * 通过角色名称查询角色列表(支持模糊)
     *
     * @param status
     * @param title
     * @return {@link List }<{@link Role }>
     * <AUTHOR> Dong
     * @date 2025/07/31 21:01:20
     */
    List<Role> findRolesByStatusAndTitleLike(String status, String title);

    /**
     * 查询所有角色
     *
     * @return {@link List }<{@link Role }>
     * <AUTHOR> Dong
     * @date 2025/08/01 10:15:03
     */
    List<Role> findAll();

    /**
     * 根据角色ID列表批量查询角色
     *
     * @param roleIds 角色ID列表
     * @return {@link List }<{@link Role }>
     * <AUTHOR> Dong
     * @date 2025/08/05 12:00:00
     */
    List<Role> findByRoleIds(List<RoleId> roleIds);
}
