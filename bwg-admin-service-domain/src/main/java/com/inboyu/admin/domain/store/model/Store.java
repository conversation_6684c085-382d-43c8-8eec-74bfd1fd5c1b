package com.inboyu.admin.domain.store.model;

import com.inboyu.admin.domain.dept.model.DeptId;
import lombok.Builder;
import lombok.Getter;

/**
 * 门店实体
 *
 * <AUTHOR> Dong
 * @date 2025年07月30日 15:30
 */
@Getter
@Builder
public class Store {
    /**
     * 门店id
     */
    private final StoreId id;
    /**
     * 归属组织ID
     */
    private final DeptId deptId;
    /**
     * 门店名称
     */
    private String title;
    /**
     * 省编码
     */
    private String provinceCode;
    /**
     * 省名称
     */
    private String provinceName;
    /**
     * 市编码
     */
    private String cityCode;
    /**
     * 市名称
     */
    private String cityName;
    /**
     * 县（区）编码
     */
    private String countyCode;
    /**
     * 县（区）名称
     */
    private String countyName;
    /**
     * 详细地址
     */
    private String address;
    /**
     * 省编码
     */
    private GeoPoint coordinate;
}
