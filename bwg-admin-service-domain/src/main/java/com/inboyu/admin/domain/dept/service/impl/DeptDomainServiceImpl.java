package com.inboyu.admin.domain.dept.service.impl;

import com.inboyu.admin.domain.dept.model.Dept;
import com.inboyu.admin.domain.dept.model.DeptCreationResult;
import com.inboyu.admin.domain.dept.model.DeptId;
import com.inboyu.admin.domain.dept.model.DeptUpdateResult;
import com.inboyu.admin.domain.dept.repository.DeptRepository;
import com.inboyu.admin.domain.dept.service.DeptDomainService;
import com.inboyu.admin.exception.ResponseCode;
import com.inboyu.spring.cloud.starter.common.exception.AppException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年07月29日 14:25
 */
@Service
public class DeptDomainServiceImpl implements DeptDomainService {

    @Autowired
    private DeptRepository deptRepository;

    /**
     * 创建组织
     *
     * @param parentDeptId
     * @param title
     * @return {@link Dept }
     * <AUTHOR>
     * @date 2025/07/30 11:09:13
     */
    @Override
    public DeptCreationResult createDept(DeptId parentDeptId, String title) {
        // 1. 校验上级组织存在
        Dept parentDept = deptRepository.findByDeptId(parentDeptId);
        if (parentDept == null) {
            throw new AppException(ResponseCode.DEPT_PARENT_NOT_EXIST);
        }
        // 2. 校验同级组织名称不重复
        if (deptRepository.countByParentDeptIdAndTitle(parentDeptId, title) > 0) {
            throw new AppException(ResponseCode.DEPT_DUPLICATE_TITLE);
        }
        // 3. 创建部门
        Dept dept = new Dept(deptRepository.generateId(), parentDeptId, title);
        // 4. 保存部门
        Dept savedDept = deptRepository.saveDept(dept);
        return new DeptCreationResult(savedDept, parentDept);
    }

    /**
     * 编辑组织
     *
     * @param deptId
     * @param title
     * @return {@link DeptUpdateResult }
     * <AUTHOR> Dong
     * @date 2025/07/31 09:30:00
     */
    @Override
    public DeptUpdateResult updateDept(DeptId deptId, String title) {
        // 1. 校验组织是否存在
        Dept existingDept = deptRepository.findByDeptId(deptId);
        if (existingDept == null) {
            throw new AppException(ResponseCode.DEPT_NOT_EXIST);
        }
        // 2. 获取父组织信息
        Dept parentDept = deptRepository.findByDeptId(existingDept.getParentDeptId());
        // 3. 校验同级组织名称不重复（排除自己）
        if (deptRepository.countByParentDeptIdAndTitleAndDeptIdNot(existingDept.getParentDeptId(), title, deptId) > 0) {
            throw new AppException(ResponseCode.DEPT_UPDATE_DUPLICATE_TITLE);
        }
        // 4. 更新组织信息
        Dept updatedDept = existingDept.updateTitle(title);
        // 5. 保存更新后的组织
        deptRepository.updateDeptTitle(updatedDept);
        return new DeptUpdateResult(updatedDept, parentDept);
    }

    /**
     * 查询所有组织
     *
     * @return {@link List <Dept> }
     * <AUTHOR> Dong
     * @date 2025/07/30 14:30:00
     */
    @Override
    public List<Dept> findAllDept() {
        return deptRepository.findAllDept();
    }

    /**
     * 根据部门id查询
     *
     * @param deptId
     * @return {@link Dept }
     * <AUTHOR> Dong
     * @date 2025/08/02 15:09:11
     */
    @Override
    public Dept findByDeptId(DeptId deptId) {
        return deptRepository.findByDeptId(deptId);
    }

    /**
     * 查询父组织下的所有子组织
     *
     * @param deptId
     * @return {@link List }<{@link Dept }>
     * <AUTHOR> Dong
     * @date 2025/08/04 15:53:43
     */
    @Override
    public List<Dept> findAllChildDept(DeptId deptId) {
        return deptRepository.findAllChildDept(deptId);
    }

    /**
     * 删除组织
     *
     * @param deptId
     * <AUTHOR> Dong
     * @date 2025/08/04 15:53:43
     */
    @Override
    public void deleteDept(DeptId deptId) {
        deptRepository.deleteDept(deptId);
    }

    /**
     * 根据部门ID列表批量查询部门
     *
     * @param deptIds 部门ID列表
     * @return {@link List }<{@link Dept }>
     * <AUTHOR> Dong
     * @date 2025/08/05 12:00:00
     */
    @Override
    public List<Dept> findByDeptIds(List<DeptId> deptIds) {
        return deptRepository.findByDeptIds(deptIds);
    }

    /**
     * 批量删除组织
     *
     * @param deptIds 组织ID列表
     * <AUTHOR> Dong
     * @date 2025/08/05 12:00:00
     */
    @Override
    public void deleteDepts(List<DeptId> deptIds) {
        deptRepository.deleteDepts(deptIds);
    }
}
