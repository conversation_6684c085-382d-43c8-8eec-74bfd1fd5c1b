package com.inboyu.admin.domain.staff.repository;

import com.inboyu.admin.domain.dept.model.DeptId;
import com.inboyu.admin.domain.staff.model.StaffDept;
import com.inboyu.admin.domain.staff.model.StaffId;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年08月05日 15:53
 */
public interface StaffRoleRepository {
    void saveAll(List<StaffDept> staffDeptList);


    /**
     * @description: 根据员工id查询员工角色
     * @author: zhouxin
     * @date: 2025/8/5 17:13
     * @param: [staffId]
     * @return: com.inboyu.admin.domain.staff.model.StaffDept
     **/
    List<StaffDept> queryByStaffId(StaffId staffId);

    Integer countByDeptIds(List<DeptId> deptIds);

    /**
     * @description: 根据员工id集合查询员工角色
     * @author: zhouxin
     * @date: 2025/8/5 19:42
     * @param: [roleIds, deptIds]
     * @return: java.util.List<com.inboyu.admin.domain.staff.model.StaffDept>
     **/
    List<StaffDept> listByRoleIdsAndDeptIds(List<String> roleIds, List<Long> deptIds);

//    List<StaffDept> listByRoleIdAndDeptIds(String roleId, List<Long> deptIds);

    void deleteByStaffId(StaffId staffId);
}
