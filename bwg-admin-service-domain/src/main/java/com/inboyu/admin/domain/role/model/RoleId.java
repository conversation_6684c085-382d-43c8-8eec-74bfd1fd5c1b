package com.inboyu.admin.domain.role.model;

import lombok.Value;

/**
 * 角色id值对象
 *
 * <AUTHOR> Dong
 * @date 2025/07/30 16:32:08
 */
@Value
public class RoleId {
    Long value;

    public RoleId(Long value) {
        if (value == null) {
            throw new IllegalArgumentException("角色id非法");
        }
        this.value = value;
    }

    public static RoleId of(Long value) {
        return new RoleId(value);
    }
}
