package com.inboyu.admin.domain.role.service.impl;

import com.inboyu.admin.domain.role.model.RoleId;
import com.inboyu.admin.domain.role.model.RolePermission;
import com.inboyu.admin.domain.role.repository.RoleScopeRepository;
import com.inboyu.admin.domain.role.service.RoleScopeDomainService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 角色权限领域服务实现
 *
 * <AUTHOR> Dong
 * @date 2025年07月31日 14:16
 */
@Service
public class RoleScopeDomainServiceImpl implements RoleScopeDomainService {
    @Autowired
    private RoleScopeRepository roleScopeRepository;

    /**
     * 批量保存角色权限
     *
     * @param rolePermissions
     * <AUTHOR> Dong
     * @date 2025/07/31 16:10:14
     */
    @Override
    public void saveAll(List<RolePermission> rolePermissions) {
        roleScopeRepository.saveAll(rolePermissions);
    }

    /**
     * 根据角色id查询权限列表
     *
     * @param roleId
     * @return {@link List }<{@link RolePermission }>
     * <AUTHOR> Dong
     * @date 2025/07/31 17:08:08
     */
    @Override
    public List<RolePermission> findByRoleId(RoleId roleId) {
        return roleScopeRepository.findByRoleId(roleId);
    }

    /**
     * 根据角色id删除角色权限
     *
     * @param id
     * <AUTHOR> Dong
     * @date 2025/07/31 19:45:16
     */
    @Override
    public void deleteByRoleId(RoleId id) {
        roleScopeRepository.deleteByRoleId(id);
    }

    /**
     * 查询角色权限
     *
     * @param roleIds
     * @return {@link Map }<{@link RoleId }, {@link List }<{@link RolePermission }>>
     * <AUTHOR> Dong
     * @date 2025/07/31 21:17:03
     */
    public Map<RoleId, List<RolePermission>> findAngGroupByRoleIds(List<RoleId> roleIds) {
        if (roleIds == null || roleIds.isEmpty()) {
            return Map.of();
        }
        // 通过仓储层查询所有角色的权限
        List<RolePermission> rolePermissions = roleScopeRepository.findByRoleIds(roleIds);
        // 按角色ID分组返回
        return rolePermissions.stream()
                .collect(Collectors.groupingBy(RolePermission::getRoleId));
    }

    /**
     * 查询角色权限
     *
     * @param roleIds
     * @return {@link List }<{@link RolePermission }>
     * <AUTHOR> Dong
     * @date 2025/08/06 10:46:01
     */
    @Override
    public List<RolePermission> findByRoleIds(List<RoleId> roleIds) {
        if (roleIds == null || roleIds.isEmpty()) {
            return Collections.emptyList();
        }
        // 通过仓储层查询所有角色的权限
        return roleScopeRepository.findByRoleIds(roleIds);
    }
}
