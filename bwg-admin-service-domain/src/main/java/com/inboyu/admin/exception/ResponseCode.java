package com.inboyu.admin.exception;

import com.inboyu.spring.cloud.starter.common.constant.ResponseCodeInterface;

/**
 * <AUTHOR>
 * @date 2025年07月29日 19:54
 */
public enum ResponseCode implements ResponseCodeInterface {

    /** 管理服务错误码范围 10000 ～ 10999*/
    DEPT_PARENT_NOT_EXIST(10000, "上级组织不存在"),
    DEPT_DUPLICATE_TITLE(10001, "组织名称重复，请检查后再新增"),
    DEPT_UPDATE_DUPLICATE_TITLE(10002, "组织名称重复，请检查后再修改"),
    DEPT_NOT_EXIST(10003, "组织不存在"),
    DEPT_DELETE_ERROR(10004, "无法删除!该组织下有门店或员工!"),
    ROLE_NOT_EXIST(1005, "角色不存在"),
    ROLE_DUPLICATE_TITLE(1006, "角色名称重复，请核对后再新增"),
    ROLE_UPDATE_DUPLICATE_TITLE(1007, "角色名称重复，请核对后再修改"),
    STORE_DEPT_NOT_EXIST(1008, "所属组织不存在"),
    STORE_DUPLICATE_TITLE(1009, "已存在相同名称的门店，请核对名称"),
    STORE_NOT_EXIST(10010, "门店不存在"),
    DICT_UPDATE_DUPLICATE_TITLE(10011, "名称重复，请检查后再操作"),
    DICT_NOT_EXIST(10012, "字典不存在"),
    STAFF_DUPLICATE_PHONE(10013, "手机号重复，请检查后再新增"),
    STAFF_UPDATE_DUPLICATE_PHONE(10014, "手机号重复，请检查后再更新"),
    STAFF_PERMISSIONS_EMPTY(10015, "启用状态下组织权限不能为空"),
    STAFF_STORES_EMPTY(10016, "启用状态下门店不能为空"),
    STAFF_ROLES_EMPTY(10017, "启用状态下角色不能为空"),
    STAFF_NOT_EXIST(10018, "员工不存在"),
    PERMISSION_NOT_EXIST(10019, "菜单权限不存在");
    /** 管理服务错误码范围 10000 ～ 10999*/
    private final int code;
    private final String msg;

    ResponseCode(int code, String des) {
        this.code = code;
        this.msg = des;
    }

    @Override
    public String getMsg() {
        return this.msg;
    }

    @Override
    public Integer getCode() {
        return this.code;
    }
}
