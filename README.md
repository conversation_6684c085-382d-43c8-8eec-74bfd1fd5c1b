## 项目概述

`bwg-admin-service` 是一个基于 Spring Cloud 构建的微服务项目，主要用于管理后台相关功能。该项目采用模块化设计，包含 API、领域模型、基础设施、应用服务和批处理等模块。

## 服务定位

提供运营商管理员工、部门、门店、角色、权限以及字典等。

## 管理服务错误码 (10000-10999)

这些错误码在 [com.inboyu.admin.exception.ResponseCode](file://./bwg-admin-service/bwg-admin-service-domain/src/main/java/com/inboyu/admin/exception/ResponseCode.java#L8-L49) 枚举中定义，用于管理系统中各种业务异常情况的标识和描述。每个错误码都有对应的中文描述信息，方便开发人员和用户理解具体的错误原因。


## 项目结构

```
bwg-admin-service/
├── bwg-admin-service-api          # API 接口定义模块
├── bwg-admin-service-app          # 应用服务模块
├── bwg-admin-service-batch        # 批处理模块
├── bwg-admin-service-domain       # 领域模型模块
├── bwg-admin-service-infrastructure # 基础设施模块
└── pom.xml                        # 父项目配置文件
```


### 各模块说明

#### 1. bwg-admin-service-api
- 作用：定义对外提供的 API 接口和数据传输对象(DTO)
- 主要内容：
    - Feign 客户端接口定义
    - 公共 DTO 类
    - API 常量定义

#### 2. bwg-admin-service-app
- 作用：应用服务层，包含业务逻辑的入口点
- 主要内容：
    - Controller 层实现
    - Application Service 实现
    - 数据转换器
    - 消息监听器
- 依赖：`bwg-admin-service-api`、`bwg-admin-service-domain`、`bwg-admin-service-infrastructure`
- 主启动类：`com.inboyu.admin.app.AdminAppApplication`

#### 3. bwg-admin-service-batch
- 作用：批处理任务模块
- 主要内容：
    - 定时任务处理
    - 批量数据处理逻辑
- 依赖：`bwg-admin-service-domain`、`bwg-admin-service-infrastructure`
- 主启动类：[com.inboyu.admin.AdminBatchApplication](file:///Users/<USER>/work/vanke/code-bwg/bwg-admin-service/bwg-admin-service-batch/src/main/java/com/inboyu/admin/AdminBatchApplication.java#L8-L18)

#### 4. bwg-admin-service-domain
- 作用：领域模型层，包含核心业务逻辑和实体定义
- 主要内容：
    - 各业务领域实体(部门、字典、权限、角色、员工、门店)
    - 领域服务接口
    - 仓储接口
    - 异常定义
- 依赖：`bwg-common`

#### 5. bwg-admin-service-infrastructure
- 作用：基础设施层，实现具体的技术细节
- 主要内容：
    - 数据访问对象(DAO)实现
    - 仓储实现
    - 构建器
    - 事件处理
- 依赖：`bwg-admin-service-domain`、`iby-spring-cloud-starter-jpa`、`iby-spring-cloud-starter-http`等


### 技术栈

- Java 语言
- Spring Boot + Spring Cloud 微服务框架
- JPA/Hibernate 作为 ORM 框架
- Redis 缓存支持
- HTTP 客户端支持
- JUnit 进行单元测试
- Maven 构建工具


### 构建与部署

项目使用 Maven 进行构建，各个子模块都有独立的打包配置。`app` 和 `batch` 模块配置了 Spring Boot Maven 插件，支持打包成可执行的 JAR 文件。

### 版本管理

项目使用统一的版本管理，通过 `${bwg.admin.service.version}` 属性控制所有子模块的版本号。