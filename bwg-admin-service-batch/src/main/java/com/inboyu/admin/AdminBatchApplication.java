package com.inboyu.admin;

import com.inboyu.alimq.annotation.EnableAliMQ;
import com.inboyu.spring.cloud.starter.web.EnableWeb;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;

@EnableFeignClients(basePackages = "com.inboyu")
@SpringBootApplication
@EnableWeb
@EnableAliMQ
public class AdminBatchApplication {

    public static void main(String[] args) {
        SpringApplication.run(AdminBatchApplication.class, args);
    }

}
