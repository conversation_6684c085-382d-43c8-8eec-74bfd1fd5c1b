package com.inboyu.admin.app.controller;

import com.alibaba.fastjson2.JSON;
import com.inboyu.admin.app.dto.request.dict.DictCreateRequestDTO;
import com.inboyu.admin.app.dto.request.dict.DictUpdateRequestDTO;
import com.inboyu.admin.app.dto.response.dict.DictCreateResponseDTO;
import com.inboyu.admin.app.dto.response.dict.DictListResponseDTO;
import com.inboyu.admin.app.dto.response.dict.DictUpdateResponseDTO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR> Dong
 * @date 2025年08月04日 16:58
 */
@SpringBootTest
public class DictControllerTest {

    @Autowired
    private DictController dictController;

    @Test
    void createDict(){
        DictCreateRequestDTO dto = new DictCreateRequestDTO();
        dto.setTitle("测试11");
        dto.setIcon("tupian.#scdafsrf./E@3adsf^^");
        dto.setIndex(3);
        dto.setRemark("测试0805");
        DictCreateResponseDTO test = dictController.createDict("table", dto);
        System.out.println("test = " + JSON.toJSONString(test));
    }

    @Test
    void createDictByNullRemark(){
        DictCreateRequestDTO dto = new DictCreateRequestDTO();
        dto.setTitle("测试2");
        dto.setIcon("tupian.#scdafsrf./E@3adsf");
        dto.setIndex(3);
        dto.setRemark(null);
        DictCreateResponseDTO test = dictController.createDict("test", dto);
        System.out.println("test = " + JSON.toJSONString(test));
    }

    @Test
    void deleteDict() {
        dictController.deleteDict("test", "test2");
    }

    @Test
    void updateDict() {
        DictUpdateRequestDTO dto = new DictUpdateRequestDTO();
        dto.setTitle("测试4");
        dto.setIcon("tupian.#scdafsrf./E@3adsf");
        dto.setIndex(4);
        dto.setRemark(null);
        DictUpdateResponseDTO dict = dictController.updateDict("test", "test4", dto);
        System.out.println("dict = " + JSON.toJSONString(dict));
    }

    @Test
    void listDict() {
        DictListResponseDTO test = dictController.listDict("test");
        System.out.println("test = " + JSON.toJSONString(test));
    }
}
