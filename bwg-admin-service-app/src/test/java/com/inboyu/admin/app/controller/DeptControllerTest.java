package com.inboyu.admin.app.controller;

import com.alibaba.fastjson2.JSON;
import com.inboyu.admin.app.dto.request.dept.DeptCreateRequestDTO;
import com.inboyu.admin.app.dto.request.dept.DeptUpdateRequestDTO;
import com.inboyu.admin.app.dto.response.dept.DeptCreateResponseDTO;
import com.inboyu.admin.app.dto.response.dept.DeptListResponseDTO;
import com.inboyu.admin.app.dto.response.dept.DeptUpdateResponseDTO;
import com.inboyu.admin.exception.ResponseCode;
import com.inboyu.spring.cloud.starter.common.exception.AppException;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

@SpringBootTest
class DeptControllerTest {

    @Autowired
    DeptController deptController;

    @Test
    void createDept() {
        DeptCreateRequestDTO dto = new DeptCreateRequestDTO();
        dto.setTitle("子组织9");
        dto.setParentDeptId("340841229455593472");
        DeptCreateResponseDTO response = deptController.createDept(dto);
        System.err.println(JSON.toJSONString(response));
    }

    @Test
    void listDept() {
        DeptListResponseDTO response = deptController.listDept();
        System.err.println(JSON.toJSONString(response));
    }

    @Test
    void updateDept() {
        DeptUpdateRequestDTO dto = new DeptUpdateRequestDTO();
        dto.setTitle("子组织8");
        DeptUpdateResponseDTO response = deptController.updateDept("346563851342700544", dto);
        System.err.println(JSON.toJSONString(response));
    }

    @Test
    void updateDeptWhenDeptIdNotExist() {
        DeptUpdateRequestDTO dto = new DeptUpdateRequestDTO();
        dto.setTitle("子组织6-1");
        DeptUpdateResponseDTO response = deptController.updateDept("11111111", dto);
        System.err.println(JSON.toJSONString(response));
    }

    @Test
    void updateDeptWhenDuplicateTitle() {
        DeptUpdateRequestDTO dto = new DeptUpdateRequestDTO();
        dto.setTitle("子组织4");
        DeptUpdateResponseDTO response = deptController.updateDept("341064508132954112", dto);
        System.err.println(JSON.toJSONString(response));
    }

    @Test
    @DisplayName("正常删除组织")
    void deleteDept() {
        String deptId = "348311298364416000";
        deptController.deleteDept(deptId);
        // 验证方法正常执行，无异常抛出
    }

    @Test
    @DisplayName("删除组织下有门店")
    void deleteDeptWhenHasStores() {
        String deptId = "341112903203688448";
        AppException exception = assertThrows(AppException.class, () -> {
            deptController.deleteDept(deptId);
        });
        assertEquals(ResponseCode.DEPT_DELETE_ERROR.getCode(), exception.getCode());
    }

    @Test
    @DisplayName("删除组织下有员工")
    void deleteDeptWhenHasStaff() {
        String deptId = "341874640391639040";
        AppException exception = assertThrows(AppException.class, () -> {
            deptController.deleteDept(deptId);
        });
        assertEquals(ResponseCode.DEPT_DELETE_ERROR.getCode(), exception.getCode());
    }

    @Test
    @DisplayName("删除组织下有子组织但子组织下没有门店和员工")
    void deleteDeptWhenHasChildDeptsNoStoresOrStaff() {
        String deptId = "341035916539858944";
        deptController.deleteDept(deptId);
        // 验证方法正常执行，无异常抛出
    }

    @Test
    @DisplayName("删除组织下有子组织且子组织下有门店")
    void deleteDeptWhenChildDeptsHaveStores() {
        String deptId = "340841229455593472";
        AppException exception = assertThrows(AppException.class, () -> {
            deptController.deleteDept(deptId);
        });
        assertEquals(ResponseCode.DEPT_DELETE_ERROR.getCode(), exception.getCode());
    }

    @Test
    @DisplayName("删除组织下有子组织且子组织下有员工")
    void deleteDeptWhenChildDeptsHaveStaff() {
        String deptId = "340841229455593472";
        AppException exception = assertThrows(AppException.class, () -> {
            deptController.deleteDept(deptId);
        });
        assertEquals(ResponseCode.DEPT_DELETE_ERROR.getCode(), exception.getCode());
    }

    @Test
    @DisplayName("删除组织不存在")
    void deleteDeptWhenDeptNotExist() {
        String deptId = "999999999999999999";
        AppException exception = assertThrows(AppException.class, () -> {
            deptController.deleteDept(deptId);
        });
        assertEquals(ResponseCode.DEPT_NOT_EXIST.getCode(), exception.getCode());
    }

    @Test
    @DisplayName("删除组织参数为null")
    void deleteDeptWhenDeptIdNull() {
        assertThrows(IllegalArgumentException.class, () -> {
            deptController.deleteDept(null);
        });
    }

    @Test
    @DisplayName("删除组织参数为负数")
    void deleteDeptWhenDeptIdNegative() {
        String deptId = "-1";
        assertThrows(IllegalArgumentException.class, () -> {
            deptController.deleteDept(deptId);
        });
    }
}
