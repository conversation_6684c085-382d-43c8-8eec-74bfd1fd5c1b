package com.inboyu.admin.app.controller.repository;

import com.alibaba.fastjson2.JSONObject;
import com.inboyu.admin.domain.staff.model.StaffDept;
import com.inboyu.admin.domain.staff.repository.StaffRoleRepository;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * @Description:
 * @Author: zhouxin
 * @Date: 2025-08-14
 */
@SpringBootTest
public class StaffRepositoryTest {

    @Autowired
    private StaffRoleRepository staffRoleRepository;


    @Test
    void listByRoleIdsAndDeptIds() {
        List<StaffDept> staffDeptList = staffRoleRepository.listByRoleIdsAndDeptIds(List.of("1"), List.of(341874640391639040L));
        System.out.println("staffDeptList = " + JSONObject.toJSONString(staffDeptList));
    }

    @Test
    void listByRoleIdsAndNoDeptIds() {
        List<StaffDept> staffDeptList = staffRoleRepository.listByRoleIdsAndDeptIds(List.of("341517076936134656", "341515475693473792"), null);
        System.out.println("staffDeptList = " + JSONObject.toJSONString(staffDeptList));
    }
}
