package com.inboyu.admin.app.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.inboyu.admin.app.dto.request.staff.StaffRequestDTO;
import com.inboyu.admin.dto.request.StaffDeptRequestDTO;
import com.inboyu.admin.dto.response.StaffDTO;
import com.inboyu.admin.dto.response.StaffDetailResponseDTO;
import com.inboyu.admin.dto.response.StaffMenuPermissionsResponseDTO;
import com.inboyu.admin.dto.response.StaffMenuResponseDTO;
import com.inboyu.admin.dto.response.StaffStatusDTO;
import com.inboyu.admin.exception.ResponseCode;
import com.inboyu.spring.cloud.starter.common.dto.Pagination;
import com.inboyu.spring.cloud.starter.common.exception.AppException;
import com.inboyu.spring.cloud.starter.context.base.utils.SystemContextUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * @Description: 员工管理测试
 * @Author: zhouxin
 * @Date: 2025-08-05
 */
@SpringBootTest
class StaffControllerTest {

    @Autowired
    StaffController staffController;

    @BeforeEach void setUp() {
        SystemContextUtils.setTenantId("123");
    }

    private StaffRequestDTO buildBaseDTO(String statusCode) {
        StaffRequestDTO dto = new StaffRequestDTO();
        dto.setName("小王");
        dto.setPhone("13111111111");
        StaffStatusDTO status = new StaffStatusDTO();
        status.setCode(statusCode);
        status.setTitle("启用");
        dto.setStatus(status);
        return dto;
    }

    @Test
    @DisplayName("正常添加启用员工")
    void createStaffSuccess() {
        StaffRequestDTO dto = buildBaseDTO("status.enabled");
        StaffDeptRequestDTO dept = new StaffDeptRequestDTO();
        dept.setDeptId("341064508132954112"); // 需为真实存在的部门ID
        dept.setIncludeAll(false);
        dept.setStoreIds(List.of("342236266449997824","342950848520916992","342235817399422976")); // 需为真实存在的门店ID
        dept.setRoleIds(List.of("341517076936134656","341515475693473792","342958031153270784")); // 需为真实存在的角色ID
        dept.setExpireTime("2025-08-05 12:00:00");
        dto.setDepts(List.of(dept));
        StaffDTO response = staffController.createStaff(dto);
        assertNotNull(response);
        assertNotNull(response.getStaffId());
    }

    @Test
    @DisplayName("启用员工组织权限为空")
    void createStaffWhenPermissionsEmpty() {
        StaffRequestDTO dto = buildBaseDTO("status.enabled");
        dto.setDepts(Collections.emptyList());
        AppException exception = assertThrows(AppException.class, () -> staffController.createStaff(dto));
        assertEquals(ResponseCode.STAFF_PERMISSIONS_EMPTY.getCode(), exception.getCode());
    }

    @Test
    @DisplayName("启用员工门店为空")
    void createStaffWhenStoreEmpty() {
        StaffRequestDTO dto = buildBaseDTO("status.enabled");
        StaffDeptRequestDTO dept = new StaffDeptRequestDTO();
        dept.setDeptId("341112903203688448");
        dept.setIncludeAll(false);
        dept.setStoreIds(null); // 门店为空
        dept.setRoleIds(List.of("341517076936134656","341515475693473792"));
        dto.setDepts(List.of(dept));
        AppException exception = assertThrows(AppException.class, () -> staffController.createStaff(dto));
        assertEquals(ResponseCode.STAFF_STORES_EMPTY.getCode(), exception.getCode());
    }

    @Test
    @DisplayName("启用员工角色为空")
    void createStaffWhenRoleEmpty() {
        StaffRequestDTO dto = buildBaseDTO("status.enabled");
        StaffDeptRequestDTO dept = new StaffDeptRequestDTO();
        dept.setDeptId("341112903203688448");
        dept.setIncludeAll(false);
        dept.setStoreIds(List.of("342236266449997824","342950848520916992"));
        dept.setRoleIds(null); // 角色为空
        dto.setDepts(List.of(dept));
        AppException exception = assertThrows(AppException.class, () -> staffController.createStaff(dto));
        assertEquals(ResponseCode.STAFF_ROLES_EMPTY.getCode(), exception.getCode());
    }

    @Test
    @DisplayName("启用员工组织不存在")
    void createStaffWhenDeptNotExist() {
        StaffRequestDTO dto = buildBaseDTO("status.enabled");
        StaffDeptRequestDTO dept = new StaffDeptRequestDTO();
        dept.setDeptId("999999"); // 不存在的部门ID
        dept.setIncludeAll(false);
        dept.setStoreIds(List.of("1"));
        dept.setRoleIds(List.of("1"));
        dto.setDepts(List.of(dept));
        AppException exception = assertThrows(AppException.class, () -> staffController.createStaff(dto));
        assertEquals(ResponseCode.DEPT_NOT_EXIST.getCode(), exception.getCode());
    }

    @Test
    @DisplayName("启用员工门店不存在")
    void createStaffWhenStoreNotExist() {
        StaffRequestDTO dto = buildBaseDTO("status.enabled");
        StaffDeptRequestDTO dept = new StaffDeptRequestDTO();
        dept.setDeptId("1");
        dept.setIncludeAll(false);
        dept.setStoreIds(List.of("999999")); // 不存在的门店ID
        dept.setRoleIds(List.of("1"));
        dto.setDepts(List.of(dept));
        AppException exception = assertThrows(AppException.class, () -> staffController.createStaff(dto));
        assertEquals(ResponseCode.STORE_NOT_EXIST.getCode(), exception.getCode());
    }

    @Test
    @DisplayName("启用员工角色不存在")
    void createStaffWhenRoleNotExist() {
        StaffRequestDTO dto = buildBaseDTO("status.enabled");
        StaffDeptRequestDTO dept = new StaffDeptRequestDTO();
        dept.setDeptId("1");
        dept.setIncludeAll(false);
        dept.setStoreIds(List.of("342236266449997824"));
        dept.setRoleIds(List.of("999999")); // 不存在的角色ID
        dto.setDepts(List.of(dept));
        AppException exception = assertThrows(AppException.class, () -> staffController.createStaff(dto));
        assertEquals(ResponseCode.ROLE_NOT_EXIST.getCode(), exception.getCode());
    }

    @Test
    void getStaffDetailTest() {
        StaffDetailResponseDTO staffDetail = staffController.getStaffDetail("343329554418503680");
        System.out.println("staffDetail = " + JSONObject.toJSONString(staffDetail));
    }

    @Test
    @DisplayName("停用员工可直接添加")
    void createStaffWhenDisabled() {
        StaffRequestDTO dto = buildBaseDTO("status.disabled");
        // 停用状态下depts可为null或空
        dto.setDepts(null);
        StaffDTO response = staffController.createStaff(dto);
        assertNotNull(response);
        assertNotNull(response.getStaffId());
    }

    @Test
    @DisplayName("参数校验失败-姓名为空")
    void createStaffWhenNameBlank() {
        StaffRequestDTO dto = buildBaseDTO("status.enabled");
        dto.setName("");
        AppException exception = assertThrows(AppException.class, () -> staffController.createStaff(dto));
        // 这里可根据实际全局异常处理返回的code断言
    }

    @Test
    @DisplayName("正常获取员工菜单权限")
    void getStaffPermissionsSuccess() {
        String staffId = "343329554418503680L"; // 使用真实存在的员工ID
        StaffMenuResponseDTO response = staffController.getStaffPermissions(staffId);
        System.err.println(JSON.toJSONString(response));
        assertNotNull(response);
        assertNotNull(response.getMenus());
        // 验证返回的菜单结构
        if (!response.getMenus().isEmpty()) {
            StaffMenuPermissionsResponseDTO menu = response.getMenus().get(0);
            assertNotNull(menu.getCode());
            assertNotNull(menu.getTitle());
        }
    }

    @Test
    @DisplayName("获取员工菜单权限-员工角色为空")
    void getStaffPermissionsWhenStaffRolesEmpty() {
        // 使用一个没有角色配置的员工ID
        String staffId = "999999L"; // 假设这个员工ID没有角色配置
        StaffMenuResponseDTO response = staffController.getStaffPermissions(staffId);
        assertNotNull(response);
        // 当员工角色为空时，应该返回空的菜单列表
        assertTrue(response.getMenus() == null || response.getMenus().isEmpty());
    }

    @Test
    @DisplayName("获取员工菜单权限-角色权限为空")
    void getStaffPermissionsWhenRolePermissionsEmpty() {
        // 使用一个角色权限为空的员工ID
        String staffId = "999998L"; // 假设这个员工ID的角色没有权限配置
        StaffMenuResponseDTO response = staffController.getStaffPermissions(staffId);
        assertNotNull(response);
        // 当角色权限为空时，应该返回空的菜单列表
        assertTrue(response.getMenus() == null || response.getMenus().isEmpty());
    }

    @Test
    @DisplayName("获取员工菜单权限-员工不存在")
    void getStaffPermissionsWhenStaffNotExist() {
        String staffId = "999999999L"; // 使用一个不存在的员工ID
        StaffMenuResponseDTO response = staffController.getStaffPermissions(staffId);
        assertNotNull(response);
        // 当员工不存在时，应该返回空的菜单列表
        assertTrue(response.getMenus() == null || response.getMenus().isEmpty());
    }

    @Test
    @DisplayName("获取员工菜单权限-员工ID为null")
    void getStaffPermissionsWhenStaffIdNull() {
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () ->
                staffController.getStaffPermissions(null));
        // 验证异常信息
        assertNotNull(exception);
    }

    @Test
    @DisplayName("列出员工-分页查询无其他条件")
    void pageByKeywordLikeSuccessTest() {
        Pagination<StaffDetailResponseDTO> staffDetail = staffController.pageByKeywordLike(1, 10, null, null, null, null);
        System.out.println("staffDetail = " + JSONObject.toJSONString(staffDetail));
    }

    @Test
    @DisplayName("列出员工-分页查询带name关键字条件")
    void pageByKeywordLikeNameSuccessTest() {
        Pagination<StaffDetailResponseDTO> staffDetail = staffController.pageByKeywordLike(1, 10, "张", null, null, null);
        System.out.println("staffDetail = " + JSONObject.toJSONString(staffDetail));
    }

    @Test
    @DisplayName("列出员工-分页查询带phone关键字条件")
    void pageByKeywordLikePhoneSuccessTest() {
        Pagination<StaffDetailResponseDTO> staffDetail = staffController.pageByKeywordLike(1, 10, "173", null, null, null);
        System.out.println("staffDetail = " + JSONObject.toJSONString(staffDetail));
    }

    @Test
    @DisplayName("列出员工-分页查询带roleId关键字条件")
    void pageByKeywordLikeRoleIdSuccessTest() {
        Pagination<StaffDetailResponseDTO> staffDetail = staffController.pageByKeywordLike(1, 10, null, "341517076936134656", null, null);
        System.out.println("staffDetail = " + JSONObject.toJSONString(staffDetail));
    }

    @Test
    @DisplayName("列出员工-分页查询带roleId和deptId关键字条件")
    void pageByKeywordLikeRoleIdsAndDeptIdsSuccessTest() {
        Pagination<StaffDetailResponseDTO> staffDetail = staffController.pageByKeywordLike(1, 10, null, "1", null, "341874640391639040");
        System.out.println("staffDetail = " + JSONObject.toJSONString(staffDetail));
    }

    @Test
    @DisplayName("列出员工-分页查询带多个roleId关键字条件")
    void pageByKeywordLikeRoleIdsSuccessTest() {
        Pagination<StaffDetailResponseDTO> staffDetail = staffController.pageByKeywordLike(1, 10, null, "1,344313435753943040", null, null);
        System.out.println("staffDetail = " + JSONObject.toJSONString(staffDetail));
    }

    @Test
    @DisplayName("列出员工-分页查询带status关键字条件")
    void pageByKeywordLikeStatusSuccessTest() {
        Pagination<StaffDetailResponseDTO> staffDetail = staffController.pageByKeywordLike(1, 10, null, null, "status.enabled", null);
        System.out.println("staffDetail = " + JSONObject.toJSONString(staffDetail));
    }

    @Test
    @DisplayName("列出员工-分页查询带phone和status关键字条件")
    void pageByKeywordLikePhoneAndStatusSuccessTest() {
        Pagination<StaffDetailResponseDTO> staffDetail = staffController.pageByKeywordLike(1, 10, "173", null, "status.enabled", null);
        System.out.println("staffDetail = " + JSONObject.toJSONString(staffDetail));
    }

    @Test
    @DisplayName("列出员工-分页查询带status和deptId关键字条件")
    void pageByStatusAndDeptIdSuccessTest() {
        Pagination<StaffDetailResponseDTO> staffDetail = staffController.pageByKeywordLike(1, 10, null, null, "status.enabled", "341874640391639040");
        System.out.println("staffDetail = " + JSONObject.toJSONString(staffDetail));
    }

    @Test
    @DisplayName("正常更新启用员工")
    void updateStaffSuccess() {
        String staffId = "343997243377979392"; // 使用真实存在的员工ID
        StaffRequestDTO dto = buildBaseDTO("status.enabled");
        StaffDeptRequestDTO dept = new StaffDeptRequestDTO();
        dept.setDeptId("341064508132954112");
        dept.setIncludeAll(true);
//        dept.setStoreIds(List.of("342236266449997824","342950848520916992"));
        dept.setRoleIds(List.of("341517076936134656","341515475693473792"));
        dto.setDepts(List.of(dept));
        StaffDTO response = staffController.updateStaff(staffId, dto);
        assertNotNull(response);
        assertEquals(staffId, response.getStaffId());
    }

    @Test
    @DisplayName("更新启用员工组织权限为空")
    void updateStaffWhenPermissionsEmpty() {
        String staffId = "343329554418503680L";
        StaffRequestDTO dto = buildBaseDTO("status.enabled");
        dto.setDepts(Collections.emptyList());
        AppException exception = assertThrows(AppException.class, () -> staffController.updateStaff(String.valueOf(staffId), dto));
        assertEquals(ResponseCode.STAFF_PERMISSIONS_EMPTY.getCode(), exception.getCode());
    }

    @Test
    @DisplayName("更新启用员工门店为空")
    void updateStaffWhenStoreEmpty() {
        String staffId = "343329554418503680";
        StaffRequestDTO dto = buildBaseDTO("status.enabled");
        StaffDeptRequestDTO dept = new StaffDeptRequestDTO();
        dept.setDeptId("341112903203688448");
        dept.setIncludeAll(false);
        dept.setStoreIds(null);
        dept.setRoleIds(List.of("341517076936134656"));
        dto.setDepts(List.of(dept));
        AppException exception = assertThrows(AppException.class, () -> staffController.updateStaff(staffId, dto));
        assertEquals(ResponseCode.STAFF_STORES_EMPTY.getCode(), exception.getCode());
    }

    @Test
    @DisplayName("更新启用员工角色为空")
    void updateStaffWhenRoleEmpty() {
        String staffId = "343329554418503680";
        StaffRequestDTO dto = buildBaseDTO("status.enabled");
        StaffDeptRequestDTO dept = new StaffDeptRequestDTO();
        dept.setDeptId("341112903203688448");
        dept.setIncludeAll(false);
        dept.setStoreIds(List.of("342236266449997824"));
        dept.setRoleIds(null);
        dto.setDepts(List.of(dept));
        AppException exception = assertThrows(AppException.class, () -> staffController.updateStaff(staffId, dto));
        assertEquals(ResponseCode.STAFF_ROLES_EMPTY.getCode(), exception.getCode());
    }

    @Test
    @DisplayName("更新启用员工组织不存在")
    void updateStaffWhenDeptNotExist() {
        String staffId = "343329554418503680";
        StaffRequestDTO dto = buildBaseDTO("status.enabled");
        StaffDeptRequestDTO dept = new StaffDeptRequestDTO();
        dept.setDeptId("999999");
        dept.setIncludeAll(false);
        dept.setStoreIds(List.of("1"));
        dept.setRoleIds(List.of("1"));
        dto.setDepts(List.of(dept));
        AppException exception = assertThrows(AppException.class, () -> staffController.updateStaff(staffId, dto));
        assertEquals(ResponseCode.DEPT_NOT_EXIST.getCode(), exception.getCode());
    }

    @Test
    @DisplayName("更新启用员工门店不存在")
    void updateStaffWhenStoreNotExist() {
        String staffId = "343329554418503680";
        StaffRequestDTO dto = buildBaseDTO("status.enabled");
        StaffDeptRequestDTO dept = new StaffDeptRequestDTO();
        dept.setDeptId("1");
        dept.setIncludeAll(false);
        dept.setStoreIds(List.of("999999"));
        dept.setRoleIds(List.of("1"));
        dto.setDepts(List.of(dept));
        AppException exception = assertThrows(AppException.class, () -> staffController.updateStaff(staffId, dto));
        assertEquals(ResponseCode.STORE_NOT_EXIST.getCode(), exception.getCode());
    }

    @Test
    @DisplayName("更新启用员工角色不存在")
    void updateStaffWhenRoleNotExist() {
        String staffId = "343329554418503680";
        StaffRequestDTO dto = buildBaseDTO("status.enabled");
        StaffDeptRequestDTO dept = new StaffDeptRequestDTO();
        dept.setDeptId("1");
        dept.setIncludeAll(false);
        dept.setStoreIds(List.of("342236266449997824"));
        dept.setRoleIds(List.of("999999"));
        dto.setDepts(List.of(dept));
        AppException exception = assertThrows(AppException.class, () -> staffController.updateStaff(staffId, dto));
        assertEquals(ResponseCode.ROLE_NOT_EXIST.getCode(), exception.getCode());
    }

    @Test
    @DisplayName("更新员工不存在")
    void updateStaffWhenStaffNotExist() {
        String staffId = "999999999"; // 不存在的员工ID
        StaffRequestDTO dto = buildBaseDTO("status.disabled");
        AppException exception = assertThrows(AppException.class, () -> staffController.updateStaff(staffId, dto));
        assertEquals(ResponseCode.STAFF_NOT_EXIST.getCode(), exception.getCode());
    }

    @Test
    @DisplayName("停用员工可直接更新")
    void updateStaffWhenDisabled() {
        String staffId = "343329554418503680";
        StaffRequestDTO dto = buildBaseDTO("status.disabled");
        dto.setDepts(null);
        StaffDTO response = staffController.updateStaff(staffId, dto);
        assertNotNull(response);
        assertEquals(staffId, response.getStaffId());
    }

    @Test
    @DisplayName("参数校验失败-姓名为空")
    void updateStaffWhenNameBlank() {
        String staffId = "343329554418503680";
        StaffRequestDTO dto = buildBaseDTO("status.enabled");
        dto.setName("");
        AppException exception = assertThrows(AppException.class, () -> staffController.updateStaff(staffId, dto));
        // 可根据实际全局异常处理返回的code断言
        assertNotNull(exception);
    }

    @Test
    void getStaffDetailByStaffId() {
        StaffDetailResponseDTO dto = staffController.getStaffDetail("343329554418503680");
        System.out.println("detail =>" + JSON.toJSONString(dto));
        assertNotNull(dto);
    }
}
