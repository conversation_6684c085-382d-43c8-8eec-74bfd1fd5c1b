package com.inboyu.admin.app.controller;

import com.alibaba.fastjson2.JSON;
import com.inboyu.admin.app.dto.request.store.StoreCreateRequestDTO;
import com.inboyu.admin.app.dto.request.store.StoreUpdateRequestDTO;
import com.inboyu.admin.app.dto.response.store.StoreCreateResponseDTO;
import com.inboyu.admin.app.dto.response.store.StoreUpdateResponseDTO;
import com.inboyu.admin.dto.common.GeoPointDTO;
import com.inboyu.admin.dto.response.StoreDetailResponseDTO;
import com.inboyu.admin.app.dto.response.store.StoreDTO;
import com.inboyu.admin.exception.ResponseCode;
import com.inboyu.spring.cloud.starter.common.dto.Pagination;
import com.inboyu.spring.cloud.starter.common.exception.AppException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * 门店控制器测试类
 *
 * 本测试类主要测试门店分页查询接口的树形组织查询功能：
 * 1. 基础分页查询功能
 * 2. 按组织ID查询（支持树形结构）
 * 3. 按关键词查询
 * 4. 组合条件查询
 * 5. 分页参数验证
 * 6. 边界值测试
 * 7. 性能测试
 *
 * 运行要求：
 * - 需要数据库中有相应的组织层级结构数据
 * - 需要数据库中有门店数据
 * - 建议在测试环境中运行，避免影响生产数据
 *
 * <AUTHOR> Dong
 * @date 2025年08月
 */
@SpringBootTest
class StoreControllerTest {

    @Autowired
    StoreController storeController;

    @Test
    @DisplayName("正常创建门店")
    void createStore() {
        StoreCreateRequestDTO dto = new StoreCreateRequestDTO();
        dto.setTitle("测试门店");
        dto.setDeptId("341112903203688448");
        dto.setProvinceCode("110000");
        dto.setProvinceName("北京市");
        dto.setCityCode("110100");
        dto.setCityName("北京市");
        dto.setCountyCode("110101");
        dto.setCountyName("东城区");
        dto.setAddress("测试地址123号");
        GeoPointDTO coordinate = new GeoPointDTO("116.397128", "39.916527");
        dto.setCoordinate(coordinate);
        StoreCreateResponseDTO response = storeController.createStore(dto);
        // 验证结果
        assertNotNull(response);
        assertNotNull(response.getStoreId());
        assertEquals("测试门店", response.getTitle());
        assertEquals("110000", response.getProvinceCode());
        assertEquals("北京市", response.getProvinceName());
        assertEquals("110100", response.getCityCode());
        assertEquals("北京市", response.getCityName());
        assertEquals("110101", response.getCountyCode());
        assertEquals("东城区", response.getCountyName());
        assertEquals("测试地址123号", response.getAddress());
        assertNotNull(response.getCoordinate());
        assertEquals("116.397128", response.getCoordinate().getLongitude());
        assertEquals("39.916527", response.getCoordinate().getLatitude());
        assertNotNull(response.getDept());
        assertEquals(341112903203688448L, response.getDept().getDeptId());
        System.out.println("创建门店成功响应: " + JSON.toJSONString(response));
    }

    @Test
    @DisplayName("组织不存在导致创建失败")
    void createStoreWhenDeptNotExist() {
        // 准备测试数据 - 使用不存在的组织ID
        StoreCreateRequestDTO dto = new StoreCreateRequestDTO();
        dto.setTitle("测试门店");
        dto.setDeptId("999999999"); // 不存在的组织ID
        dto.setProvinceCode("110000");
        dto.setProvinceName("北京市");
        dto.setCityCode("110100");
        dto.setCityName("北京市");
        dto.setCountyCode("110101");
        dto.setCountyName("东城区");
        dto.setAddress("测试地址123号");
        GeoPointDTO coordinate = new GeoPointDTO("116.397128", "39.916527");
        dto.setCoordinate(coordinate);
        // 执行测试并验证异常
        AppException exception = assertThrows(AppException.class, () -> {
            storeController.createStore(dto);
        });
        // 验证异常信息
        assertEquals(ResponseCode.STORE_DEPT_NOT_EXIST.getCode(), exception.getCode());
        assertEquals(ResponseCode.STORE_DEPT_NOT_EXIST.getMsg(), exception.getMessage());
        System.out.println("组织不存在异常: " + exception.getMessage());
    }

    @Test
    @DisplayName("组织ID为空导致参数验证失败")
    void createStoreWhenDeptIdNull() {
        // 准备测试数据 - 组织ID为空
        StoreCreateRequestDTO dto = new StoreCreateRequestDTO();
        dto.setTitle("测试门店");
        dto.setDeptId(null); // 空组织ID
        dto.setProvinceCode("110000");
        dto.setProvinceName("北京市");
        dto.setCityCode("110100");
        dto.setCityName("北京市");
        dto.setCountyCode("110101");
        dto.setCountyName("东城区");
        dto.setAddress("测试地址123号");

        GeoPointDTO coordinate = new GeoPointDTO("116.397128", "39.916527");
        dto.setCoordinate(coordinate);

        // 执行测试并验证参数验证异常
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            storeController.createStore(dto);
        });
        System.out.println("组织为空异常: " + exception.getMessage());
    }

    @Test
    @DisplayName("门店名称重复导致参数验证失败")
    void createStoreWhenTitleDuplicate() {
        // 准备测试数据 - 组织ID为空
        StoreCreateRequestDTO dto = new StoreCreateRequestDTO();
        dto.setTitle("测试门店");
        dto.setDeptId("341112903203688448");
        dto.setProvinceCode("110000");
        dto.setProvinceName("北京市");
        dto.setCityCode("110100");
        dto.setCityName("北京市");
        dto.setCountyCode("110101");
        dto.setCountyName("东城区");
        dto.setAddress("测试地址123号");

        GeoPointDTO coordinate = new GeoPointDTO("116.397128", "39.916527");
        dto.setCoordinate(coordinate);
        // 执行测试并验证异常
        AppException exception = assertThrows(AppException.class, () -> {
            storeController.createStore(dto);
        });
        // 验证异常信息
        assertEquals(ResponseCode.STORE_DUPLICATE_TITLE.getCode(), exception.getCode());
        assertEquals(ResponseCode.STORE_DUPLICATE_TITLE.getMsg(), exception.getMessage());
        System.out.println("门店名称异常: " + exception.getMessage());
    }

    // ==================== 门店更新接口测试 ====================

    @Test
    @DisplayName("正常更新门店")
    void updateStore() {
        // 首先创建一个门店用于更新
        StoreCreateRequestDTO createDto = new StoreCreateRequestDTO();
        createDto.setTitle("原始门店");
        createDto.setDeptId("341112903203688448");
        createDto.setProvinceCode("110000");
        createDto.setProvinceName("北京市");
        createDto.setCityCode("110100");
        createDto.setCityName("北京市");
        createDto.setCountyCode("110101");
        createDto.setCountyName("东城区");
        createDto.setAddress("原始地址123号");
        GeoPointDTO createCoordinate = new GeoPointDTO("116.397128", "39.916527");
        createDto.setCoordinate(createCoordinate);

        StoreCreateResponseDTO createResponse = storeController.createStore(createDto);
        String storeId = createResponse.getStoreId();

        // 准备更新数据
        StoreUpdateRequestDTO updateDto = new StoreUpdateRequestDTO();
        updateDto.setTitle("更新后的门店");
        updateDto.setDeptId("341112903203688448");
        updateDto.setProvinceCode("120000");
        updateDto.setProvinceName("天津市");
        updateDto.setCityCode("120100");
        updateDto.setCityName("天津市");
        updateDto.setCountyCode("120101");
        updateDto.setCountyName("和平区");
        updateDto.setAddress("更新后的地址456号");
        GeoPointDTO updateCoordinate = new GeoPointDTO("116.397128", "39.916527");
        updateDto.setCoordinate(updateCoordinate);

        // 执行更新
        StoreUpdateResponseDTO response = storeController.updateStore(storeId, updateDto);

        // 验证结果
        assertNotNull(response);
        assertEquals(storeId, response.getStoreId());
        assertEquals("更新后的门店", response.getTitle());
        assertEquals("120000", response.getProvinceCode());
        assertEquals("天津市", response.getProvinceName());
        assertEquals("120100", response.getCityCode());
        assertEquals("天津市", response.getCityName());
        assertEquals("120101", response.getCountyCode());
        assertEquals("和平区", response.getCountyName());
        assertEquals("更新后的地址456号", response.getAddress());
        assertNotNull(response.getCoordinate());
        assertEquals("117.190182", response.getCoordinate().getLongitude());
        assertEquals("39.125596", response.getCoordinate().getLatitude());
        assertNotNull(response.getDept());
        assertEquals(341112903203688448L, response.getDept().getDeptId());
        System.out.println("更新门店成功响应: " + JSON.toJSONString(response));
    }

    @Test
    @DisplayName("门店不存在导致更新失败")
    void updateStoreWhenStoreNotExist() {
        // 准备更新数据
        StoreUpdateRequestDTO updateDto = new StoreUpdateRequestDTO();
        updateDto.setTitle("更新后的门店");
        updateDto.setDeptId("341112903203688448");
        updateDto.setProvinceCode("120000");
        updateDto.setProvinceName("天津市");
        updateDto.setCityCode("120100");
        updateDto.setCityName("天津市");
        updateDto.setCountyCode("120101");
        updateDto.setCountyName("和平区");
        updateDto.setAddress("更新后的地址456号");
        GeoPointDTO coordinate = new GeoPointDTO("116.397128", "39.916527");
        updateDto.setCoordinate(coordinate);

        // 使用不存在的门店ID
        String nonExistentStoreId = "999999999";

        // 执行测试并验证异常
        AppException exception = assertThrows(AppException.class, () -> {
            storeController.updateStore(nonExistentStoreId, updateDto);
        });

        // 验证异常信息
        assertEquals(ResponseCode.STORE_NOT_EXIST.getCode(), exception.getCode());
        assertEquals(ResponseCode.STORE_NOT_EXIST.getMsg(), exception.getMessage());
        System.out.println("门店不存在异常: " + exception.getMessage());
    }

    @Test
    @DisplayName("组织不存在导致更新失败")
    void updateStoreWhenDeptNotExist() {
        // 首先创建一个门店用于更新
        StoreCreateRequestDTO createDto = new StoreCreateRequestDTO();
        createDto.setTitle("原始门店");
        createDto.setDeptId("341112903203688448");
        createDto.setProvinceCode("110000");
        createDto.setProvinceName("北京市");
        createDto.setCityCode("110100");
        createDto.setCityName("北京市");
        createDto.setCountyCode("110101");
        createDto.setCountyName("东城区");
        createDto.setAddress("原始地址123号");
        GeoPointDTO createCoordinate = new GeoPointDTO("116.397128", "39.916527");
        createDto.setCoordinate(createCoordinate);

        StoreCreateResponseDTO createResponse = storeController.createStore(createDto);
        String storeId = createResponse.getStoreId();

        // 准备更新数据 - 使用不存在的组织ID
        StoreUpdateRequestDTO updateDto = new StoreUpdateRequestDTO();
        updateDto.setTitle("更新后的门店");
        updateDto.setDeptId("999999999"); // 不存在的组织ID
        updateDto.setProvinceCode("120000");
        updateDto.setProvinceName("天津市");
        updateDto.setCityCode("120100");
        updateDto.setCityName("天津市");
        updateDto.setCountyCode("120101");
        updateDto.setCountyName("和平区");
        updateDto.setAddress("更新后的地址456号");
        GeoPointDTO coordinate = new GeoPointDTO("116.397128", "39.916527");
        updateDto.setCoordinate(coordinate);

        // 执行测试并验证异常
        AppException exception = assertThrows(AppException.class, () -> {
            storeController.updateStore(storeId, updateDto);
        });

        // 验证异常信息
        assertEquals(ResponseCode.STORE_DEPT_NOT_EXIST.getCode(), exception.getCode());
        assertEquals(ResponseCode.STORE_DEPT_NOT_EXIST.getMsg(), exception.getMessage());
        System.out.println("组织不存在异常: " + exception.getMessage());
    }

    @Test
    @DisplayName("门店名称重复导致更新失败")
    void updateStoreWhenTitleDuplicate() {
        // 首先创建两个门店
        StoreCreateRequestDTO createDto1 = new StoreCreateRequestDTO();
        createDto1.setTitle("门店A");
        createDto1.setDeptId("341112903203688448");
        createDto1.setProvinceCode("110000");
        createDto1.setProvinceName("北京市");
        createDto1.setCityCode("110100");
        createDto1.setCityName("北京市");
        createDto1.setCountyCode("110101");
        createDto1.setCountyName("东城区");
        createDto1.setAddress("地址A");
        GeoPointDTO coordinate1 = new GeoPointDTO("116.397128", "39.916527");
        createDto1.setCoordinate(coordinate1);
        StoreCreateResponseDTO response1 = storeController.createStore(createDto1);
        StoreCreateRequestDTO createDto2 = new StoreCreateRequestDTO();
        createDto2.setTitle("门店B");
        createDto2.setDeptId("341112903203688448");
        createDto2.setProvinceCode("110000");
        createDto2.setProvinceName("北京市");
        createDto2.setCityCode("110100");
        createDto2.setCityName("北京市");
        createDto2.setCountyCode("110101");
        createDto2.setCountyName("东城区");
        createDto2.setAddress("地址B");
        GeoPointDTO coordinate2 = new GeoPointDTO("116.397128", "39.916527");
        createDto2.setCoordinate(coordinate2);
        StoreCreateResponseDTO response2 = storeController.createStore(createDto2);
        String storeId2 = response2.getStoreId();
        // 尝试将门店B的名称更新为门店A的名称
        StoreUpdateRequestDTO updateDto = new StoreUpdateRequestDTO();
        updateDto.setTitle("门店A"); // 与门店A重名
        updateDto.setDeptId("341112903203688448");
        updateDto.setProvinceCode("110000");
        updateDto.setProvinceName("北京市");
        updateDto.setCityCode("110100");
        updateDto.setCityName("北京市");
        updateDto.setCountyCode("110101");
        updateDto.setCountyName("东城区");
        updateDto.setAddress("地址B");
        updateDto.setCoordinate(coordinate2);

        // 执行测试并验证异常
        AppException exception = assertThrows(AppException.class, () -> {
            storeController.updateStore(storeId2, updateDto);
        });

        // 验证异常信息
        assertEquals(ResponseCode.STORE_DUPLICATE_TITLE.getCode(), exception.getCode());
        assertEquals(ResponseCode.STORE_DUPLICATE_TITLE.getMsg(), exception.getMessage());
        System.out.println("门店名称重复异常: " + exception.getMessage());
    }

    @Test
    @DisplayName("参数验证失败 - 组织ID为空")
    void updateStoreWhenDeptIdNull() {
        // 首先创建一个门店用于更新
        StoreCreateRequestDTO createDto = new StoreCreateRequestDTO();
        createDto.setTitle("测试门店A");
        createDto.setDeptId("341112903203688448");
        createDto.setProvinceCode("110000");
        createDto.setProvinceName("北京市");
        createDto.setCityCode("110100");
        createDto.setCityName("北京市");
        createDto.setCountyCode("110101");
        createDto.setCountyName("东城区");
        createDto.setAddress("原始地址123号");
        GeoPointDTO createCoordinate = new GeoPointDTO("116.397128", "39.916527");
        createDto.setCoordinate(createCoordinate);

        StoreCreateResponseDTO createResponse = storeController.createStore(createDto);
        String storeId = createResponse.getStoreId();

        // 准备更新数据 - 组织ID为空
        StoreUpdateRequestDTO updateDto = new StoreUpdateRequestDTO();
        updateDto.setTitle("更新后的门店");
        updateDto.setDeptId(null); // 空组织ID
        updateDto.setProvinceCode("120000");
        updateDto.setProvinceName("天津市");
        updateDto.setCityCode("120100");
        updateDto.setCityName("天津市");
        updateDto.setCountyCode("120101");
        updateDto.setCountyName("和平区");
        updateDto.setAddress("更新后的地址456号");
        GeoPointDTO coordinate = new GeoPointDTO("116.397128", "39.916527");
        updateDto.setCoordinate(coordinate);

        // 执行测试并验证参数验证异常
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            storeController.updateStore(storeId, updateDto);
        });
        System.out.println("组织ID为空异常: " + exception.getMessage());
    }

    @Test
    @DisplayName("更新门店时保持名称不变")
    void updateStoreWithSameTitle() {
        // 首先创建一个门店用于更新
        StoreCreateRequestDTO createDto = new StoreCreateRequestDTO();
        createDto.setTitle("测试门店C");
        createDto.setDeptId("341112903203688448");
        createDto.setProvinceCode("110000");
        createDto.setProvinceName("北京市");
        createDto.setCityCode("110100");
        createDto.setCityName("北京市");
        createDto.setCountyCode("110101");
        createDto.setCountyName("东城区");
        createDto.setAddress("原始地址123号");
        GeoPointDTO createCoordinate = new GeoPointDTO("116.397128", "39.916527");
        createDto.setCoordinate(createCoordinate);

        StoreCreateResponseDTO createResponse = storeController.createStore(createDto);
        String storeId = createResponse.getStoreId();

        // 准备更新数据 - 保持名称不变，只更新其他字段
        StoreUpdateRequestDTO updateDto = new StoreUpdateRequestDTO();
        updateDto.setTitle("测试门店C"); // 保持相同名称
        updateDto.setDeptId("341112903203688448");
        updateDto.setProvinceCode("120000");
        updateDto.setProvinceName("天津市");
        updateDto.setCityCode("120100");
        updateDto.setCityName("天津市");
        updateDto.setCountyCode("120101");
        updateDto.setCountyName("和平区");
        updateDto.setAddress("更新后的地址456号");
        GeoPointDTO updateCoordinate = new GeoPointDTO("116.397128", "39.916527");
        updateDto.setCoordinate(updateCoordinate);

        // 执行更新
        StoreUpdateResponseDTO response = storeController.updateStore(storeId, updateDto);

        // 验证结果
        assertNotNull(response);
        assertEquals(storeId, response.getStoreId());
        assertEquals("测试门店C", response.getTitle()); // 名称保持不变
        assertEquals("120000", response.getProvinceCode());
        assertEquals("天津市", response.getProvinceName());
        assertEquals("120100", response.getCityCode());
        assertEquals("天津市", response.getCityName());
        assertEquals("120101", response.getCountyCode());
        assertEquals("和平区", response.getCountyName());
        assertEquals("更新后的地址456号", response.getAddress());
        assertNotNull(response.getCoordinate());
        assertEquals("117.190182", response.getCoordinate().getLongitude());
        assertEquals("39.125596", response.getCoordinate().getLatitude());
        System.out.println("保持名称不变的更新成功: " + JSON.toJSONString(response));
    }

    // ==================== 门店详情查询接口测试 ====================

    @Test
    @DisplayName("正常查询门店详情")
    void getStore() {
        // 首先创建一个门店用于查询
        StoreCreateRequestDTO createDto = new StoreCreateRequestDTO();
        createDto.setTitle("测试查询门店");
        createDto.setDeptId("341112903203688448");
        createDto.setProvinceCode("110000");
        createDto.setProvinceName("北京市");
        createDto.setCityCode("110100");
        createDto.setCityName("北京市");
        createDto.setCountyCode("110101");
        createDto.setCountyName("东城区");
        createDto.setAddress("测试查询地址123号");
        GeoPointDTO coordinate = new GeoPointDTO("116.397128", "39.916527");
        createDto.setCoordinate(coordinate);

        StoreCreateResponseDTO createResponse = storeController.createStore(createDto);
        String storeId = createResponse.getStoreId();

        // 查询门店详情
        StoreDetailResponseDTO response = storeController.getStore(storeId);

        // 验证结果
        assertNotNull(response);
        assertEquals(storeId, response.getStoreId());
        assertEquals("测试查询门店", response.getTitle());
        assertEquals("110000", response.getProvinceCode());
        assertEquals("北京市", response.getProvinceName());
        assertEquals("110100", response.getCityCode());
        assertEquals("北京市", response.getCityName());
        assertEquals("110101", response.getCountyCode());
        assertEquals("东城区", response.getCountyName());
        assertEquals("测试查询地址123号", response.getAddress());
        assertNotNull(response.getCoordinate());
        assertEquals("116.397128", response.getCoordinate().getLongitude());
        assertEquals("39.916527", response.getCoordinate().getLatitude());
        assertNotNull(response.getDept());
        assertEquals("341112903203688448", response.getDept().getDeptId());
        assertNotNull(response.getDept().getTitle());
        System.out.println("查询门店详情成功响应: " + JSON.toJSONString(response));
    }

    @Test
    @DisplayName("门店不存在导致查询失败")
    void getStoreWhenStoreNotExist() {
        // 使用不存在的门店ID
        String nonExistentStoreId = "999999999";

        // 执行测试并验证异常
        AppException exception = assertThrows(AppException.class, () -> {
            storeController.getStore(nonExistentStoreId);
        });

        // 验证异常信息
        assertEquals(ResponseCode.STORE_NOT_EXIST.getCode(), exception.getCode());
        assertEquals(ResponseCode.STORE_NOT_EXIST.getMsg(), exception.getMessage());
        System.out.println("门店不存在异常: " + exception.getMessage());
    }

    @Test
    @DisplayName("门店所属组织不存在导致查询失败")
    void getStoreWhenDeptNotExist() {
        // 这个测试需要模拟门店存在但所属组织不存在的情况
        // 由于这是业务逻辑测试，需要在实际环境中验证
        // 这里提供一个测试框架，实际执行时需要确保数据一致性

        // 首先创建一个门店
        StoreCreateRequestDTO createDto = new StoreCreateRequestDTO();
        createDto.setTitle("测试门店详情查询");
        createDto.setDeptId("341112903203688448");
        createDto.setProvinceCode("110000");
        createDto.setProvinceName("北京市");
        createDto.setCityCode("110100");
        createDto.setCityName("北京市");
        createDto.setCountyCode("110101");
        createDto.setCountyName("东城区");
        createDto.setAddress("测试地址123号");
        GeoPointDTO coordinate = new GeoPointDTO("116.397128", "39.916527");
        createDto.setCoordinate(coordinate);

        StoreCreateResponseDTO createResponse = storeController.createStore(createDto);
        String storeId = createResponse.getStoreId();

        // 正常情况下应该能查询到门店详情
        StoreDetailResponseDTO response = storeController.getStore(storeId);
        assertNotNull(response);
        assertEquals(storeId, response.getStoreId());
        System.out.println("正常查询门店详情: " + JSON.toJSONString(response));
    }

    @Test
    @DisplayName("查询门店详情 - 验证返回数据结构完整性")
    void getStoreValidateDataStructure() {
        // 首先创建一个门店用于查询
        StoreCreateRequestDTO createDto = new StoreCreateRequestDTO();
        createDto.setTitle("数据结构测试门店");
        createDto.setDeptId("341112903203688448");
        createDto.setProvinceCode("120000");
        createDto.setProvinceName("天津市");
        createDto.setCityCode("120100");
        createDto.setCityName("天津市");
        createDto.setCountyCode("120101");
        createDto.setCountyName("和平区");
        createDto.setAddress("数据结构测试地址456号");
        GeoPointDTO coordinate = new GeoPointDTO("116.397128", "39.916527");
        createDto.setCoordinate(coordinate);

        StoreCreateResponseDTO createResponse = storeController.createStore(createDto);
        String storeId = createResponse.getStoreId();

        // 查询门店详情
        StoreDetailResponseDTO response = storeController.getStore(storeId);

        // 验证数据结构完整性
        assertNotNull(response);

        // 验证门店基本信息
        assertNotNull(response.getStoreId());
        assertNotNull(response.getTitle());
        assertFalse(response.getTitle().isEmpty());

        // 验证部门信息
        assertNotNull(response.getDept());
        assertNotNull(response.getDept().getDeptId());
        assertNotNull(response.getDept().getTitle());
        assertFalse(response.getDept().getTitle().isEmpty());

        // 验证地理位置信息
        assertNotNull(response.getProvinceCode());
        assertNotNull(response.getProvinceName());
        assertNotNull(response.getCityCode());
        assertNotNull(response.getCityName());
        assertNotNull(response.getCountyCode());
        assertNotNull(response.getCountyName());
        assertNotNull(response.getAddress());

        // 验证坐标信息
        assertNotNull(response.getCoordinate());
        assertNotNull(response.getCoordinate().getLongitude());
        assertNotNull(response.getCoordinate().getLatitude());
        assertFalse(response.getCoordinate().getLongitude().isEmpty());
        assertFalse(response.getCoordinate().getLatitude().isEmpty());

        System.out.println("数据结构完整性验证通过: " + JSON.toJSONString(response));
    }

    @Test
    @DisplayName("查询门店详情 - 边界值测试")
    void getStoreBoundaryTest() {
        // 测试边界值：门店ID为0
        String zeroStoreId = "0";

        AppException exception = assertThrows(AppException.class, () -> {
            storeController.getStore(zeroStoreId);
        });

        assertEquals(ResponseCode.STORE_NOT_EXIST.getCode(), exception.getCode());
        System.out.println("边界值测试 - 门店ID为0: " + exception.getMessage());

        // 测试边界值：门店ID为负数
        String negativeStoreId = "-1";

        AppException negativeException = assertThrows(AppException.class, () -> {
            storeController.getStore(negativeStoreId);
        });

        assertEquals(ResponseCode.STORE_NOT_EXIST.getCode(), negativeException.getCode());
        System.out.println("边界值测试 - 门店ID为负数: " + negativeException.getMessage());
    }

    @Test
    @DisplayName("查询门店详情 - 并发测试")
    void getStoreConcurrencyTest() throws InterruptedException {
        // 首先创建一个门店用于并发查询
        StoreCreateRequestDTO createDto = new StoreCreateRequestDTO();
        createDto.setTitle("并发测试门店");
        createDto.setDeptId("341112903203688448");
        createDto.setProvinceCode("110000");
        createDto.setProvinceName("北京市");
        createDto.setCityCode("110100");
        createDto.setCityName("北京市");
        createDto.setCountyCode("110101");
        createDto.setCountyName("东城区");
        createDto.setAddress("并发测试地址123号");
        GeoPointDTO coordinate = new GeoPointDTO("116.397128", "39.916527");
        createDto.setCoordinate(coordinate);

        StoreCreateResponseDTO createResponse = storeController.createStore(createDto);
        String storeId = createResponse.getStoreId();

        // 创建多个线程并发查询
        int threadCount = 5;
        Thread[] threads = new Thread[threadCount];
        StoreDetailResponseDTO[] results = new StoreDetailResponseDTO[threadCount];

        for (int i = 0; i < threadCount; i++) {
            final int index = i;
            threads[i] = new Thread(() -> {
                try {
                    results[index] = storeController.getStore(storeId);
                } catch (Exception e) {
                    System.err.println("线程 " + index + " 查询失败: " + e.getMessage());
                }
            });
        }

        // 启动所有线程
        for (Thread thread : threads) {
            thread.start();
        }

        // 等待所有线程完成
        for (Thread thread : threads) {
            thread.join();
        }

        // 验证所有线程都返回了相同的结果
        StoreDetailResponseDTO firstResult = results[0];
        assertNotNull(firstResult);

        for (int i = 1; i < threadCount; i++) {
            if (results[i] != null) {
                assertEquals(firstResult.getStoreId(), results[i].getStoreId());
                assertEquals(firstResult.getTitle(), results[i].getTitle());
                assertEquals(firstResult.getAddress(), results[i].getAddress());
            }
        }

        System.out.println("并发测试通过，所有线程返回一致结果");
    }

    @Test
    @DisplayName("查询门店详情 - 性能测试")
    void getStorePerformanceTest() {
        // 首先创建一个门店用于性能测试
        StoreCreateRequestDTO createDto = new StoreCreateRequestDTO();
        createDto.setTitle("性能测试门店");
        createDto.setDeptId("341112903203688448");
        createDto.setProvinceCode("110000");
        createDto.setProvinceName("北京市");
        createDto.setCityCode("110100");
        createDto.setCityName("北京市");
        createDto.setCountyCode("110101");
        createDto.setCountyName("东城区");
        createDto.setAddress("性能测试地址123号");
        GeoPointDTO coordinate = new GeoPointDTO("116.397128", "39.916527");
        createDto.setCoordinate(coordinate);

        StoreCreateResponseDTO createResponse = storeController.createStore(createDto);
        String storeId = createResponse.getStoreId();

        // 执行多次查询并记录时间
        int testCount = 10;
        long totalTime = 0;
        for (int i = 0; i < testCount; i++) {
            long startTime = System.currentTimeMillis();
            StoreDetailResponseDTO response = storeController.getStore(storeId);
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            totalTime += duration;
            // 验证每次查询都返回正确结果
            assertNotNull(response);
            assertEquals(storeId, response.getStoreId());
            System.out.println("第 " + (i + 1) + " 次查询耗时: " + duration + "ms");
        }
        long averageTime = totalTime / testCount;
        System.out.println("平均查询耗时: " + averageTime + "ms");
        System.out.println("总查询耗时: " + totalTime + "ms");
        // 性能断言：平均查询时间应该在合理范围内（比如小于100ms）
        assertTrue(averageTime < 100, "平均查询时间超过100ms: " + averageTime + "ms");
    }

    @Test
    @DisplayName("分页查询门店 - 基础功能测试")
    void pageStores() {
        // 测试基础分页查询
        Pagination<StoreDTO> stores = storeController.pageStores(1, 20, null, null);
        assertNotNull(stores);
        assertNotNull(stores.getList());
        System.out.println("基础分页查询结果: " + JSON.toJSONString(stores));
    }

    @Test
    @DisplayName("分页查询门店 - 按组织ID查询")
    void pageStoresByDeptId() {
        // 测试按指定组织ID查询
        String deptId = "341874640391639040";
        Pagination<StoreDTO> stores = storeController.pageStores(1, 20, null, deptId);
        assertNotNull(stores);
        assertNotNull(stores.getList());

        // 验证返回的门店数据
        if (!stores.getList().isEmpty()) {
            for (StoreDTO store : stores.getList()) {
                assertNotNull(store.getStoreId());
                assertNotNull(store.getTitle());
                System.out.println("门店: " + store.getTitle() + ", 门店ID: " + store.getStoreId());
            }
        }
        System.out.println("按组织ID查询结果: " + JSON.toJSONString(stores));
    }

    @Test
    @DisplayName("分页查询门店 - 按关键词查询")
    void pageStoresByKeywords() {
        // 测试按关键词查询
        String keywords = "科";
        Pagination<StoreDTO> stores = storeController.pageStores(1, 20, keywords, null);
        assertNotNull(stores);
        assertNotNull(stores.getList());

        // 验证返回的门店名称都包含关键词
        if (!stores.getList().isEmpty()) {
            for (StoreDTO store : stores.getList()) {
                assertTrue(store.getTitle().contains(keywords),
                    "门店名称应该包含关键词: " + store.getTitle());
            }
        }
        System.out.println("按关键词查询结果: " + JSON.toJSONString(stores));
    }

    @Test
    @DisplayName("分页查询门店 - 组合条件查询")
    void pageStoresWithCombinedConditions() {
        // 测试组合条件查询：组织ID + 关键词
        String deptId = "341874640391639040";
        String keywords = "科";
        Pagination<StoreDTO> stores = storeController.pageStores(1, 20, keywords, deptId);
        assertNotNull(stores);
        assertNotNull(stores.getList());

        // 验证返回的门店满足所有条件
        if (!stores.getList().isEmpty()) {
            for (StoreDTO store : stores.getList()) {
                assertTrue(store.getTitle().contains(keywords),
                    "门店名称应该包含关键词: " + store.getTitle());
                assertNotNull(store.getStoreId());
            }
        }
        System.out.println("组合条件查询结果: " + JSON.toJSONString(stores));
    }

    @Test
    @DisplayName("分页查询门店 - 分页参数测试")
    void pageStoresPaginationTest() {
        // 测试分页参数
        int pageNum = 1;
        int pageSize = 5;

        Pagination<StoreDTO> stores = storeController.pageStores(pageNum, pageSize, null, null);
        assertNotNull(stores);
        assertEquals(pageNum, stores.getPageNum());
        assertEquals(pageSize, stores.getPageSize());
        assertTrue(stores.getList().size() <= pageSize,
            "返回结果数量应该不超过页面大小");

        System.out.println("分页参数测试结果: " + JSON.toJSONString(stores));
    }

    @Test
    @DisplayName("分页查询门店 - 边界值测试")
    void pageStoresBoundaryTest() {
        // 测试边界值
        // 第一页
        Pagination<StoreDTO> firstPage = storeController.pageStores(1, 10, null, null);
        assertNotNull(firstPage);
        assertEquals(1, firstPage.getPageNum());

        // 如果有多页，测试第二页
        if (firstPage.getTotalPages() > 1) {
            Pagination<StoreDTO> secondPage = storeController.pageStores(2, 10, null, null);
            assertNotNull(secondPage);
            assertEquals(2, secondPage.getPageNum());
            assertNotEquals(firstPage.getList().getFirst().getStoreId(),
                secondPage.getList().getFirst().getStoreId());
        }

        System.out.println("边界值测试完成");
    }

    @Test
    @DisplayName("分页查询门店 - 空结果测试")
    void pageStoresEmptyResultTest() {
        // 测试查询不存在的关键词
        String nonExistentKeywords = "不存在的门店名称12345";
        Pagination<StoreDTO> stores = storeController.pageStores(1, 20, nonExistentKeywords, null);
        assertNotNull(stores);
        assertNotNull(stores.getList());
        assertEquals(0, stores.getList().size());
        assertEquals(0, stores.getTotal());

        System.out.println("空结果测试通过");
    }

    @Test
    @DisplayName("分页查询门店 - 树形组织查询测试")
    void pageStoresTreeDeptTest() {
        // 测试树形组织查询功能
        // 使用一个上级组织ID，验证是否能查询到子组织下的门店
        String parentDeptId = "340841229455593472"; // 假设这是一个上级组织ID

        // 查询该组织下的门店
        Pagination<StoreDTO> stores = storeController.pageStores(1, 50, null, parentDeptId);
        assertNotNull(stores);
        assertNotNull(stores.getList());

        if (!stores.getList().isEmpty()) {
            System.out.println("树形组织查询结果:");
            for (StoreDTO store : stores.getList()) {
                System.out.println("门店: " + store.getTitle() +
                    ", 门店ID: " + store.getStoreId() +
                    ", 地址: " + store.getAddress());
            }

            // 验证返回的门店数量
            assertTrue(stores.getTotal() > 0, "应该查询到门店数据");
        } else {
            System.out.println("该组织下暂无门店数据");
        }

        System.out.println("树形组织查询测试完成，总门店数: " + stores.getTotal());
    }

    @Test
    @DisplayName("分页查询门店 - 不存在的组织ID测试")
    void pageStoresWithNonExistentDeptId() {
        // 测试使用不存在的组织ID
        String nonExistentDeptId = "999999999999999999";
        Pagination<StoreDTO> stores = storeController.pageStores(1, 20, null, nonExistentDeptId);
        assertNotNull(stores);
        assertNotNull(stores.getList());

        // 应该返回空结果，因为组织不存在
        assertEquals(0, stores.getList().size());
        assertEquals(0, stores.getTotal());

        System.out.println("不存在的组织ID测试通过，返回空结果");
    }

    @Test
    @DisplayName("分页查询门店 - 空字符串组织ID测试")
    void pageStoresWithEmptyDeptId() {
        // 测试空字符串组织ID
        String emptyDeptId = "";
        Pagination<StoreDTO> stores = storeController.pageStores(1, 20, null, emptyDeptId);
        assertNotNull(stores);
        assertNotNull(stores.getList());

        // 空字符串应该被忽略，返回所有门店
        assertTrue(stores.getTotal() >= 0);

        System.out.println("空字符串组织ID测试通过");
    }

    @Test
    @DisplayName("分页查询门店 - 特殊字符关键词测试")
    void pageStoresWithSpecialCharacters() {
        // 测试包含特殊字符的关键词
        String specialKeywords = "测试@#$%";
        Pagination<StoreDTO> stores = storeController.pageStores(1, 20, specialKeywords, null);
        assertNotNull(stores);
        assertNotNull(stores.getList());

        // 验证结果
        if (!stores.getList().isEmpty()) {
            for (StoreDTO store : stores.getList()) {
                assertTrue(store.getTitle().contains(specialKeywords),
                    "门店名称应该包含特殊字符关键词: " + store.getTitle());
            }
        }

        System.out.println("特殊字符关键词测试通过");
    }

    @Test
    @DisplayName("分页查询门店 - 大数据量分页测试")
    void pageStoresWithLargePageSize() {
        // 测试大页面大小
        int largePageSize = 100;
        Pagination<StoreDTO> stores = storeController.pageStores(1, largePageSize, null, null);
        assertNotNull(stores);
        assertEquals(1, stores.getPageNum());
        assertEquals(largePageSize, stores.getPageSize());
        assertTrue(stores.getList().size() <= largePageSize);

        System.out.println("大数据量分页测试通过，页面大小: " + largePageSize + ", 实际返回: " + stores.getList().size());
    }

    @Test
    @DisplayName("分页查询门店 - 性能基准测试")
    void pageStoresPerformanceTest() {
        // 性能基准测试
        long startTime = System.currentTimeMillis();

        // 执行多次查询取平均值
        int testCount = 5;
        long totalTime = 0;

        for (int i = 0; i < testCount; i++) {
            long queryStart = System.currentTimeMillis();
            Pagination<StoreDTO> stores = storeController.pageStores(1, 20, null, null);
            long queryEnd = System.currentTimeMillis();
            long duration = queryEnd - queryStart;
            totalTime += duration;

            assertNotNull(stores);
            System.out.println("第 " + (i + 1) + " 次查询耗时: " + duration + "ms");
        }

        long endTime = System.currentTimeMillis();
        long averageTime = totalTime / testCount;
        long totalDuration = endTime - startTime;

        System.out.println("性能测试完成:");
        System.out.println("平均查询耗时: " + averageTime + "ms");
        System.out.println("总测试耗时: " + totalDuration + "ms");

        // 性能断言：平均查询时间应该在合理范围内
        assertTrue(averageTime < 200, "平均查询时间超过200ms: " + averageTime + "ms");
    }

    @Test
    @DisplayName("分页查询门店 - 树形组织查询核心逻辑测试")
    void pageStoresTreeDeptCoreLogicTest() {
        // 这个测试验证树形组织查询的核心逻辑
        // 通过创建多个层级的组织和门店来验证查询功能

        // 注意：这个测试需要数据库中有相应的组织层级结构
        // 如果测试环境没有合适的数据，可以跳过或标记为集成测试

        System.out.println("开始树形组织查询核心逻辑测试...");

        // 测试场景1：查询上级组织，应该包含子组织下的门店
        String parentDeptId = "341874640391639040";
        Pagination<StoreDTO> parentStores = storeController.pageStores(1, 100, null, parentDeptId);
        assertNotNull(parentStores);
        System.out.println("上级组织查询结果数量: " + parentStores.getTotal());

        // 测试场景2：查询所有门店（不指定组织）
        Pagination<StoreDTO> allStores = storeController.pageStores(1, 100, null, null);
        assertNotNull(allStores);
        System.out.println("所有门店查询结果数量: " + allStores.getTotal());

        // 验证：如果指定了组织ID，返回的门店数量应该小于等于所有门店数量
        if (parentStores.getTotal() > 0) {
            assertTrue(parentStores.getTotal() <= allStores.getTotal(),
                "指定组织的门店数量应该小于等于所有门店数量");
            System.out.println("树形组织查询逻辑验证通过");
        } else {
            System.out.println("该组织下暂无门店数据，跳过数量验证");
        }

        // 测试场景3：验证分页功能在树形查询中的表现
        if (parentStores.getTotal() > 10) {
            // 如果数据量足够，测试分页
            Pagination<StoreDTO> firstPage = storeController.pageStores(1, 10, null, parentDeptId);
            Pagination<StoreDTO> secondPage = storeController.pageStores(2, 10, null, parentDeptId);

            assertEquals(10, firstPage.getList().size());
            assertTrue(secondPage.getList().size() > 0);

            // 验证两页数据不重复
            if (!firstPage.getList().isEmpty() && !secondPage.getList().isEmpty()) {
                String firstStoreId = firstPage.getList().get(0).getStoreId();
                String secondStoreId = secondPage.getList().get(0).getStoreId();
                assertNotEquals(firstStoreId, secondStoreId, "分页数据不应该重复");
            }

            System.out.println("树形组织查询分页功能验证通过");
        }

        System.out.println("树形组织查询核心逻辑测试完成");
    }

    @Test
    @DisplayName("根据门店ID获取门店信息")
    void filterSuccess() {
        // 执行查询操作
        List<StoreDTO> list = storeController.filter("342232754680893440");

        // 打印查询结果（方便调试）
        System.out.println("查询结果: " + JSON.toJSONString(list));

        // 验证结果不为null
        assertNotNull(list, "查询结果不应为null");

        assertFalse(list.isEmpty(), "查询结果不应为空集合");

        // 例如：验证返回的门店ID与查询ID一致（如果适用）
        assertEquals("342232754680893440", list.getFirst().getStoreId(), "门店ID不匹配");
    }

    @Test
    @DisplayName("预期错误门店ID会抛出异常")
    void filterError() {
        // 预期方法会抛出异常
        assertThrows(IllegalArgumentException.class, () -> {
            // 执行包含错误ID的查询操作
            List<StoreDTO> list = storeController.filter("342232754680893440,x");
            // 如果没有抛出异常，打印结果（实际不应该执行到这里）
            System.out.println(JSON.toJSONString(list));
        });
    }
}
