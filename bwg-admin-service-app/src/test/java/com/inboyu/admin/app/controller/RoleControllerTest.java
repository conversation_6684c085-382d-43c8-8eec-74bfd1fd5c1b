package com.inboyu.admin.app.controller;

import com.alibaba.fastjson.JSON;
import com.inboyu.admin.app.dto.common.role.RoleDTO;
import com.inboyu.admin.app.dto.common.role.RolePermissionDTO;
import com.inboyu.admin.app.dto.common.role.RoleStatusDTO;
import com.inboyu.admin.app.dto.request.role.RoleCreateRequestDTO;
import com.inboyu.admin.app.dto.request.role.RoleUpdateRequestDTO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@SpringBootTest
class RoleControllerTest {

    @Autowired
    RoleController roleController;

    @Test
    void createRole() {
        RoleCreateRequestDTO req = new RoleCreateRequestDTO();
        req.setTitle("测试角色1");
        req.setRemark("");
        RoleStatusDTO status = new RoleStatusDTO();
        status.setCode("status.enabled");
        status.setTitle("启用");
        req.setStatus(status);
        req.setPermission(new HashMap<>());
        RoleDTO result = roleController.createRole(req);
        System.err.println(JSON.toJSONString(result));
    }

    @Test
    void createRole2() {
        RoleCreateRequestDTO req = new RoleCreateRequestDTO();
        req.setTitle("店长111");
        req.setRemark("门店店长角色");
        RoleStatusDTO status = new RoleStatusDTO();
        status.setCode("status.enabled");
        status.setTitle("启用");
        req.setStatus(status);

        // 创建权限映射，支持一个菜单组下有多个权限
        Map<String, List<RolePermissionDTO>> permissions = new HashMap<>();

        List<RolePermissionDTO> userPermissions = new ArrayList<>();
        RolePermissionDTO createUser = new RolePermissionDTO();
        createUser.setPermission("tenant.roles.insert");
        createUser.setHidden(false);
        createUser.setDisabled(false);
        userPermissions.add(createUser);

        RolePermissionDTO updateUser = new RolePermissionDTO();
        updateUser.setPermission("tenant.roles.detail");
        updateUser.setHidden(false);
        updateUser.setDisabled(false);
        userPermissions.add(updateUser);

        permissions.put("Admin/Roles", userPermissions);

        List<RolePermissionDTO> rolePermissions = new ArrayList<>();
        RolePermissionDTO queryRole = new RolePermissionDTO();
        queryRole.setPermission("tenant.dict.insert");
        queryRole.setHidden(false);
        queryRole.setDisabled(false);
        rolePermissions.add(queryRole);

        permissions.put("Admin/Dict", rolePermissions);

        req.setPermission(permissions);
        RoleDTO result = roleController.createRole(req);
        System.err.println(JSON.toJSONString(result));
    }

    @Test
    void getRole() {
        RoleDTO result = roleController.getRole("346529338310852608");
        System.err.println(JSON.toJSONString(result));
    }

    @Test
    void getRoleWhenNotExist() {
        RoleDTO result = roleController.getRole("1111");
        System.err.println(JSON.toJSONString(result));
    }

    @Test
    void updateRole() {
        RoleUpdateRequestDTO dto = new RoleUpdateRequestDTO();
        dto.setTitle("高级店长");
        dto.setRemark("高级门店店长角色，拥有更多权限");
        RoleStatusDTO status = new RoleStatusDTO();
        status.setCode("status.enabled");
        status.setTitle("启用");
        dto.setStatus(status);

        // 创建完整的权限映射
        Map<String, List<RolePermissionDTO>> permissions = new HashMap<>();

        List<RolePermissionDTO> userPermissions = new ArrayList<>();
        RolePermissionDTO createUser = new RolePermissionDTO();
        createUser.setPermission("tenant.staffs.insert");
        createUser.setHidden(false);
        createUser.setDisabled(false);
        userPermissions.add(createUser);

        RolePermissionDTO updateUser = new RolePermissionDTO();
        updateUser.setPermission("tenant.staffs.detail");
        updateUser.setHidden(false);
        updateUser.setDisabled(false);
        userPermissions.add(updateUser);

        RolePermissionDTO deleteUser = new RolePermissionDTO();
        deleteUser.setPermission("tenant.staffs.update");
        deleteUser.setHidden(false);
        deleteUser.setDisabled(false);
        userPermissions.add(deleteUser);

        permissions.put("Admin/Staffs", userPermissions);

        // 角色管理权限
        List<RolePermissionDTO> rolePermissions = new ArrayList<>();
        RolePermissionDTO createRole = new RolePermissionDTO();
        createRole.setPermission("tenant.stores.insert");
        createRole.setHidden(false);
        createRole.setDisabled(false);
        rolePermissions.add(createRole);

        RolePermissionDTO updateRole = new RolePermissionDTO();
        updateRole.setPermission("tenant.stores.detail");
        updateRole.setHidden(false);
        updateRole.setDisabled(false);
        rolePermissions.add(updateRole);

        permissions.put("Admin/Stores", rolePermissions);

        // 门店管理权限
        List<RolePermissionDTO> storePermissions = new ArrayList<>();
        RolePermissionDTO queryStore = new RolePermissionDTO();
        queryStore.setPermission("tenant.dict.insert");
        queryStore.setHidden(false);
        queryStore.setDisabled(false);
        storePermissions.add(queryStore);

        permissions.put("Admin/Dict", storePermissions);
        permissions.put("Admin/Roles", List.of());


        dto.setPermission(permissions);
        RoleDTO result = roleController.updateRole("344364470338076672", dto);
        System.err.println(JSON.toJSONString(result));
    }

    @Test
    void listRoles() {
        List<RoleDTO> result = roleController.listRoles(null, null);
        System.err.println(JSON.toJSONString(result));
    }

    @Test
    void listRolesByTitle() {
        List<RoleDTO> result = roleController.listRoles("店长", null);
        System.err.println(JSON.toJSONString(result));
    }

    @Test
    void listRolesByStatusAndTitle() {
        List<RoleDTO> result = roleController.listRoles("店长", "status.enabled");
        System.err.println(JSON.toJSONString(result));
    }
}
