package com.inboyu.admin.app.dto.request.dept;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 编辑组织请求实体
 *
 * <AUTHOR>
 * @date 2025年07月31日 09:09
 */
@Data
public class DeptUpdateRequestDTO {
    @Schema(description = "组织名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "组织名称不能为空")
    private String title;
}
