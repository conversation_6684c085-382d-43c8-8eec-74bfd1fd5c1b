package com.inboyu.admin.app.application.impl;

import com.alibaba.fastjson2.JSONObject;
import com.inboyu.admin.app.application.StaffAppService;
import com.inboyu.admin.app.dto.converter.StaffAppConverter;
import com.inboyu.admin.app.dto.converter.StaffDeptAppConverter;
import com.inboyu.admin.app.dto.request.staff.StaffRequestDTO;
import com.inboyu.admin.domain.dept.model.Dept;
import com.inboyu.admin.domain.dept.model.DeptId;
import com.inboyu.admin.domain.dept.service.DeptDomainService;
import com.inboyu.admin.domain.permission.model.MenuPermission;
import com.inboyu.admin.domain.permission.service.PermissionDomainService;
import com.inboyu.admin.domain.role.model.Role;
import com.inboyu.admin.domain.role.model.RoleId;
import com.inboyu.admin.domain.role.model.RolePermission;
import com.inboyu.admin.domain.role.service.RoleDomainService;
import com.inboyu.admin.domain.role.service.RoleScopeDomainService;
import com.inboyu.admin.domain.staff.model.Staff;
import com.inboyu.admin.domain.staff.model.StaffDept;
import com.inboyu.admin.domain.staff.model.StaffId;
import com.inboyu.admin.domain.staff.model.StaffRoleResult;
import com.inboyu.admin.domain.staff.service.StaffDomainService;
import com.inboyu.admin.domain.staff.service.StaffRoleDomainService;
import com.inboyu.admin.domain.store.model.Store;
import com.inboyu.admin.domain.store.model.StoreId;
import com.inboyu.admin.domain.store.service.StoreDomainService;
import com.inboyu.admin.dto.request.StaffDeptRequestDTO;
import com.inboyu.admin.dto.response.StaffDTO;
import com.inboyu.admin.dto.response.StaffDetailResponseDTO;
import com.inboyu.admin.dto.response.StaffMenuPermissionsResponseDTO;
import com.inboyu.admin.exception.ResponseCode;
import com.inboyu.admin.infrastructure.constant.StaffStatusEnum;
import com.inboyu.spring.cloud.starter.common.dto.Pagination;
import com.inboyu.spring.cloud.starter.common.exception.AppException;
import com.inboyu.spring.cloud.starter.redis.lock.DistributedLock;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Dong
 * @date 2025年08月05日 11:23
 */
@Service
@Log4j2
public class StaffAppServiceImpl implements StaffAppService {

    @Autowired
    private StaffDomainService staffDomainService;

    @Autowired
    private DeptDomainService deptDomainService;

    @Autowired
    private StoreDomainService storeDomainService;

    @Autowired
    private RoleDomainService roleDomainService;

    @Autowired
    private StaffAppConverter staffAppConverter;

    @Autowired
    private StaffRoleDomainService staffRoleDomainService;

    @Autowired
    private StaffDeptAppConverter staffDeptAppConverter;

    @Autowired
    private PermissionDomainService permissionDomainService;

    @Autowired
    private RoleScopeDomainService roleScopeDomainService;

    /**
     * 创建员工
     *
     * @param dto
     * @return {@link StaffDTO }
     * <AUTHOR> Dong
     * @date 2025/08/05 11:24:47
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @DistributedLock(key = "#dto.phone", transType = "locker_createStaff_")
    public StaffDTO createStaff(StaffRequestDTO dto) {
        // 1. 校验员工状态，如果为启用状态，需要校验组织权限配置
        if (dto.getStatus() != null && StaffStatusEnum.ENABLED.getCode().equals(dto.getStatus().getCode())) {
            validateStaffPermissions(dto);
        }
        // 2. 生成员工ID
        StaffId staffId = staffDomainService.generateId();
        // 3. 转换员工信息
        Staff staff = staffAppConverter.toStaff(staffId, dto);
        // 4. 创建员工
        Staff createdStaff = staffDomainService.createStaff(staff);
        // 5. 创建员工部门关联
        List<StaffDept> staffDeptList = staffDeptAppConverter.toStaffDeptList(staffId, dto.getDepts());
        if (ObjectUtils.isNotEmpty(staffDeptList)) {
            staffRoleDomainService.saveAll(staffDeptList);
        }
        // 6 发送创建员工事件
        staffDomainService.sendCreateEvent(createdStaff);
        // 7. 转换并返回响应
        return staffAppConverter.toStaffAddResponseDTO(createdStaff, staffDeptList);
    }


    /**
     * 校验员工权限配置
     *
     * @param dto 员工添加请求DTO
     * <AUTHOR> Dong
     * @date 2025/08/05 11:50:00
     */
    private void validateStaffPermissions(StaffRequestDTO dto) {
        if (CollectionUtils.isEmpty(dto.getDepts())) {
            log.error("新增/编辑员工失败,原因:启用状态的员工组织权限不能为空,员工姓名:{}", dto.getName());
            throw new AppException(ResponseCode.STAFF_PERMISSIONS_EMPTY);
        }
        // 收集所有需要校验的ID
        Set<Long> deptIds = new HashSet<>();
        Set<Long> storeIds = new HashSet<>();
        Set<Long> roleIds = new HashSet<>();
        for (StaffDeptRequestDTO deptDto : dto.getDepts()) {
            deptIds.add(Long.valueOf(deptDto.getDeptId()));
            // 校验门店配置
            if (Boolean.FALSE.equals(deptDto.getIncludeAll()) && ObjectUtils.isEmpty(deptDto.getStoreIds())) {
                log.error("新增/编辑员工失败,原因:启用状态的员工门店不能为空,员工姓名:{},部门id:{}", dto.getName(), deptDto.getDeptId());
                throw new AppException(ResponseCode.STAFF_STORES_EMPTY);
            }
            if (ObjectUtils.isNotEmpty(deptDto.getStoreIds())) {
                storeIds.addAll(deptDto.getStoreIds().stream().map(Long::valueOf).toList());
            }
            // 校验角色配置
            if (CollectionUtils.isEmpty(deptDto.getRoleIds())) {
                log.error("新增/编辑员工失败,原因:启用状态的员工角色不能为空,员工姓名:{},部门id:{}", dto.getName(), deptDto.getDeptId());
                throw new AppException(ResponseCode.STAFF_ROLES_EMPTY);
            }
            roleIds.addAll(deptDto.getRoleIds().stream().map(Long::valueOf).toList());
        }
        // 批量查询并校验部门
        List<DeptId> deptIdList = deptIds.stream().map(DeptId::of).toList();
        List<Dept> depts = deptDomainService.findByDeptIds(deptIdList);
        if (depts.size() != deptIds.size()) {
            Set<Long> foundDeptIds = depts.stream().map(dept -> dept.getId().getValue()).collect(Collectors.toSet());
            List<Long> missingDeptIds = deptIds.stream().filter(id -> !foundDeptIds.contains(id)).toList();
            log.error("新增/编辑员工失败,原因:部门不存在,员工姓名:{},部门ids:{}", dto.getName(), missingDeptIds);
            throw new AppException(ResponseCode.DEPT_NOT_EXIST);
        }
        // 批量查询并校验门店
        if (!storeIds.isEmpty()) {
            List<StoreId> storeIdList = storeIds.stream().map(StoreId::of).toList();
            List<Store> stores = storeDomainService.findByStoreIds(storeIdList);
            if (stores.size() != storeIds.size()) {
                Set<Long> foundStoreIds = stores.stream().map(store -> store.getId().getValue()).collect(Collectors.toSet());
                List<Long> missingStoreIds = storeIds.stream().filter(id -> !foundStoreIds.contains(id)).toList();
                log.error("新增/编辑员工失败,原因:门店不存在,员工姓名:{},门店ids:{}", dto.getName(), missingStoreIds);
                throw new AppException(ResponseCode.STORE_NOT_EXIST);
            }
        }
        // 批量查询并校验角色
        List<RoleId> roleIdList = roleIds.stream().map(RoleId::of).toList();
        List<Role> roles = roleDomainService.findByRoleIds(roleIdList);
        if (roles.size() != roleIds.size()) {
            Set<Long> foundRoleIds = roles.stream().map(role -> role.getId().getValue()).collect(Collectors.toSet());
            List<Long> missingRoleIds = roleIds.stream().filter(id -> !foundRoleIds.contains(id)).toList();
            log.error("新增/编辑员工失败,原因:角色不存在,员工姓名:{},角色ids:{}", dto.getName(), missingRoleIds);
            throw new AppException(ResponseCode.ROLE_NOT_EXIST);
        }
    }

    @Override
    public StaffDetailResponseDTO getStaffDetail(Long staffId) {
        Staff byStaffId = staffDomainService.findByStaffId(StaffId.of(staffId));
        if (null == byStaffId) {
            log.error("查询员工详情失败,原因:员工不存在,员工id:{}", staffId);
            throw new AppException(ResponseCode.STAFF_NOT_EXIST);
        }
        List<StaffRoleResult> staffRoleResults = staffRoleDomainService.listDeptStaff(byStaffId.getId());
        return staffAppConverter.toStaffDetailResponseDTO(byStaffId, staffRoleResults);
    }

    /**
     * 员工运营系统菜单及权限
     *
     * @param staffId
     * @return {@link List }<{@link StaffMenuPermissionsResponseDTO }>
     * <AUTHOR> Dong
     * @date 2025/08/06 09:22:27
     */
    @Override
    public List<StaffMenuPermissionsResponseDTO> getStaffPermissions(Long staffId) {
        MenuPermission permissionsTree = permissionDomainService.getPermissionsTree();
        if (null == permissionsTree || ObjectUtils.isEmpty(permissionsTree.getMenus())) {
            log.info("查询运营系统菜单权限返回为空");
            return Collections.emptyList();
        }
        // 查询员工组织角色列表
        List<StaffDept> staffRoles = staffRoleDomainService.findByStaffId(StaffId.of(staffId));
        // 获取角色列表
        List<RoleId> roleIds = staffDeptAppConverter.getRoleIdList(staffRoles);
        // 查询角色权限
        List<RolePermission> rolePermissions = roleScopeDomainService.findByRoleIds(roleIds);
        return staffDeptAppConverter.toMenuPermissions(rolePermissions, permissionsTree, staffRoles);
    }

    @Override
    public Pagination<StaffDetailResponseDTO> pageByKeywordLike(
            Integer pageNum, Integer pageSize,
            String keyword, String status,
            String roleId, String deptId) {

        // 1. 预处理查询条件
        final List<String> roleIds = getRoleIds(roleId);
        final List<Long> deptIdList = resolveDeptIds(deptId);

        // 2. 查询员工角色列表
        Result result = getStaffRolesResult(roleIds, deptIdList);

        // 3. 分页查询员工数据（带过滤条件）
        List<Long> staffIdList = new ArrayList<>(result.staffIds());
        Pagination<Staff> staffPage = staffDomainService.pageByKeywordLike(pageNum, pageSize, keyword, status, staffIdList);

        if (staffPage == null || CollectionUtils.isEmpty(staffPage.getList())) {
            return new Pagination<>(pageNum, pageSize);
        }

        // 4. 处理员工和员工角色
        Map<Long, List<StaffRoleResult>> staffRolesMap = fetchStaffRoles(result.staffRoleResults());

        // 5. 构建响应数据
        List<StaffDetailResponseDTO> responseList = buildResponseList(staffPage.getList(), staffRolesMap, roleIds, deptIdList);

        // 6. 计算分页信息
        return buildPaginationResult(staffPage, responseList, roleIds, deptIdList);
    }

    /**
     * @description: 获取员工角色信息
     * @author: zhouxin
     * @date: 2025/8/14 10:13
     * @param: [roleIds, deptIdList]
     * @return: com.inboyu.admin.app.application.impl.StaffAppServiceImpl.Result
     **/
    private Result getStaffRolesResult(List<String> roleIds, List<Long> deptIdList) {
        List<StaffRoleResult> staffRoleResults = staffRoleDomainService.listByRoleIdsAndDeptIds(roleIds, deptIdList);
        System.out.println("staffRoleResults = " + JSONObject.toJSONString(staffRoleResults));
        Set<Long> staffIds = staffRoleResults.stream().map(StaffRoleResult::getStaffId).map(StaffId::getValue).collect(Collectors.toSet());
        return new Result(staffRoleResults, staffIds);
    }

    private record Result(List<StaffRoleResult> staffRoleResults, Set<Long> staffIds) {
    }

    /**
     * @description: 解析员工ID集合
     * @author: zhouxin
     * @date: 2025/8/14 9:56
     * @param: [roleId]
     * @return: java.util.List<java.lang.String>
     **/
    private List<String> getRoleIds(String roleId) {
        if (StringUtils.isNotBlank(roleId)) {
            if (roleId.contains(",")) {
                String[] roleIdList = roleId.split(",");
                return Arrays.stream(roleIdList).toList();
            } else {
                return List.of(roleId);
            }
        }
        return List.of();
    }

    /**
     * @description: 构建部门id列表
     * @author: zhouxin
     * @date: 2025/8/13 20:15
     * @param: [deptId]
     * @return: java.util.List<java.lang.Long>
     **/
    private List<Long> resolveDeptIds(String deptId) {
        if (StringUtils.isBlank(deptId)) {
            return Collections.emptyList();
        }

        Long rootDeptId = Long.valueOf(deptId);
        List<Dept> allChildDept = deptDomainService.findAllChildDept(DeptId.of(rootDeptId));
        List<Dept> deptList = new ArrayList<>(allChildDept);
        deptList.add(Dept.builder().id(DeptId.of(Long.valueOf(deptId))).build());
        return deptList.stream().map(Dept::getId).map(DeptId::getValue).toList();
    }

    /**
     * @description: 构建员工组织权限
     * @author: zhouxin
     * @date: 2025/8/13 20:16
     * @param: [staffIds, roleId, deptIds]
     * @return: java.util.Map<java.lang.Long,java.util.List<com.inboyu.admin.domain.staff.model.StaffRoleResult>>
     **/
    private Map<Long, List<StaffRoleResult>> fetchStaffRoles(List<StaffRoleResult> staffRoleResults) {
        return staffRoleResults.stream()
                .filter(Objects::nonNull)
                .filter(result -> result.getStaffId() != null)
                .collect(Collectors.groupingBy(
                        result -> result.getStaffId().getValue()
                ));
    }

    /**
     * @description: 构建员工列表
     * @author: zhouxin
     * @date: 2025/8/13 20:16
     * @param: [staffList, staffRolesMap, roleIds, deptIdList]
     * @return: java.util.List<com.inboyu.admin.app.dto.response.staff.StaffDetailResponseDTO>
     **/
    private List<StaffDetailResponseDTO> buildResponseList(
            List<Staff> staffList,
            Map<Long, List<StaffRoleResult>> staffRolesMap,
            List<String> roleIds,
            List<Long> deptIdList) {

        return staffList.stream()
                .map(staff -> {
                    Long staffId = staff.getId().getValue();
                    List<StaffRoleResult> roles = staffRolesMap.getOrDefault(staffId, Collections.emptyList());
                    return staffAppConverter.toStaffDetailResponseDTO(staff, roles);
                })
                .filter(dto -> filterByRole(dto, roleIds))
                .filter(dto -> filterByDept(dto, deptIdList))
                .collect(Collectors.toList());
    }

    /**
     * @description: 根据角色过滤员工
     * @author: zhouxin
     * @date: 2025/8/13 20:16
     * @param: [dto, roleIds]
     * @return: boolean
     **/
    private boolean filterByRole(StaffDetailResponseDTO dto, List<String> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return true;
        }

        return dto.getDepts().stream()
                .flatMap(dept -> dept.getRoles().stream())
                .anyMatch(role -> roleIds.contains(role.getRoleId()));
    }

    /**
     * @description: 根据部门过滤员工
     * @author: zhouxin
     * @date: 2025/8/13 20:17
     * @param: [dto, deptIdList]
     * @return: boolean
     **/
    private boolean filterByDept(StaffDetailResponseDTO dto, List<Long> deptIdList) {
        if (CollectionUtils.isEmpty(deptIdList)) {
            return true;
        }

        return dto.getDepts().stream()
                .anyMatch(dept -> deptIdList.contains(Long.valueOf(dept.getDeptId())));
    }

    /**
     * @description: 构建分页响应数据
     * @author: zhouxin
     * @date: 2025/8/13 20:17
     * @param: [staffPage, responseList, roleId, deptIdList]
     * @return: com.inboyu.spring.cloud.starter.common.dto.Pagination<com.inboyu.admin.app.dto.response.staff.StaffDetailResponseDTO>
     **/
    private Pagination<StaffDetailResponseDTO> buildPaginationResult(
            Pagination<Staff> staffPage,
            List<StaffDetailResponseDTO> responseList,
            List<String> roleIds,
            List<Long> deptIdList) {

        int total = (int) staffPage.getTotal();
        int filteredSize = responseList.size();

        // 如果应用了内存过滤，需要重新计算分页信息
        if (!CollectionUtils.isEmpty(roleIds) || !CollectionUtils.isEmpty(deptIdList)) {
            int totalPages = (int) Math.ceil((double) filteredSize / staffPage.getPageSize());
            return new Pagination<>(
                    staffPage.getPageNum() + 1,
                    staffPage.getPageSize(),
                    totalPages,
                    filteredSize,
                    responseList
            );
        }

        // 没有内存过滤时，直接使用原始分页信息
        return new Pagination<>(
                staffPage.getPageNum() + 1,
                staffPage.getPageSize(),
                staffPage.getTotalPages(),
                total,
                responseList
        );
    }

    /**
     * 更新员工
     *
     * @param staffId
     * @param dto
     * @return {@link StaffDTO }
     * <AUTHOR> Dong
     * @date 2025/08/06 15:10:20
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @DistributedLock(key = "#staffId", transType = "locker_updateStaff_")
    public StaffDTO updateStaff(Long staffId, StaffRequestDTO dto) {
        // 1.校验员工状态，如果为启用状态，需要校验组织权限配置
        if (dto.getStatus() != null && StaffStatusEnum.ENABLED.getCode().equals(dto.getStatus().getCode())) {
            validateStaffPermissions(dto);
        }
        // 2.生成员工ID
        StaffId id = StaffId.of(staffId);
        // 3.查询员工
        Staff existStaff = staffDomainService.findByStaffId(id);
        if (existStaff == null) {
            log.error("编辑员工失败,原因:员工不存在,员工id:{}", staffId);
            throw new AppException(ResponseCode.STAFF_NOT_EXIST);
        }
        // 4.转换员工信息
        Staff staff = staffAppConverter.toStaff(id, dto);
        // 5.更新员工
        Staff updateStaff = staffDomainService.updateStaff(staff);
        // 6. 创建员工部门关联
        List<StaffDept> staffDeptList = staffDeptAppConverter.toStaffDeptList(id, dto.getDepts());
        // 7. 全量删除并新增组织、角色、门店关系
        staffRoleDomainService.updateStaffRoles(id, staffDeptList);
        // 8 发送更新员工事件
        staffDomainService.sendUpdateEvent(existStaff.getUserId(), updateStaff);
        // 9. 返回DTO
        return staffAppConverter.toStaffAddResponseDTO(updateStaff, staffDeptList);
    }


    @Override
    public List<StaffMenuPermissionsResponseDTO> getAllPermission() {
        MenuPermission permissionsTree = permissionDomainService.getPermissionsTree();
        List<Store> stores = storeDomainService.getAll();
        return staffDeptAppConverter.toMenuPermissions(permissionsTree, stores);
    }
}
