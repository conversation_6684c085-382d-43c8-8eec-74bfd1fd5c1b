package com.inboyu.admin.app.dto.common.role;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 角色状态
 *
 * <AUTHOR>
 * @date 2025年07月30日 17:17
 */
@Data
public class RoleStatusDTO {
    /**
     * 状态编码
     */
    @Schema(description = "状态编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "状态编码不能为空")
    private String code;
    /**
     * 角色状态描述
     */
    @Schema(description = "状态描述", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "状态描述不能为空")
    private String title;
}
