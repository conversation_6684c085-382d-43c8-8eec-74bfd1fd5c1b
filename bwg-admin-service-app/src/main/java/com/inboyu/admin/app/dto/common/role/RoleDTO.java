package com.inboyu.admin.app.dto.common.role;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class RoleDTO {
    /**
     * 角色ID
     */
    @Schema(description = "角色ID")
    private String roleId;
    /**
     * 角色名称
     */
    @Schema(description = "角色名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "角色名称不能为空")
    private String title;
    /**
     * 角色描述
     */
    @Schema(description = "角色描述")
    private String remark;
    /**
     * 角色权限
     */
    @Schema(description = "角色权限")
    private Map<String, List<RolePermissionDTO>> permission;
    /**
     * 角色状态
     */
    @Schema(description = "角色状态", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "状态必选")
    private RoleStatusDTO status;
}
