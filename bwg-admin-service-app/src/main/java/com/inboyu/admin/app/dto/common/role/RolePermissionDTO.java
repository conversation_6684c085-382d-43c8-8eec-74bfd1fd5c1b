package com.inboyu.admin.app.dto.common.role;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025年07月30日 17:14
 */
@Data
public class RolePermissionDTO {
    /**
     * 权限编码
     */
    @Schema(description = "是否隐藏")
    private String permission;
    /**
     * 是否隐藏
     */
    @Schema(description = "是否隐藏")
    private boolean hidden;
    /**
     * 是否可用
     */
    @Schema(description = "是否可用")
    private boolean disabled;
}
