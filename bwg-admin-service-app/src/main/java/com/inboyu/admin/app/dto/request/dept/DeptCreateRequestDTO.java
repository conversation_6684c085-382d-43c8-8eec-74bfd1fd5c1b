package com.inboyu.admin.app.dto.request.dept;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 添加组织请求实体
 *
 * <AUTHOR> Dong
 * @date 2025年07月29日 14:36
 */
@Data
public class DeptCreateRequestDTO {
    @Schema(description = "组织名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "组织名称不能为空")
    private String title;
    @Schema(description = "上级组织", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "上级组织不能为空")
    private String parentDeptId;
}
