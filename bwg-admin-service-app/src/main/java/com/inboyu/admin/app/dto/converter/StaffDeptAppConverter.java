package com.inboyu.admin.app.dto.converter;

import com.alibaba.fastjson2.JSON;
import com.inboyu.admin.domain.dept.model.DeptId;
import com.inboyu.admin.domain.permission.model.MenuPermission;
import com.inboyu.admin.domain.permission.model.MenuPermission.MenuDTO;
import com.inboyu.admin.domain.role.model.RoleId;
import com.inboyu.admin.domain.role.model.RolePermission;
import com.inboyu.admin.domain.staff.model.StaffDept;
import com.inboyu.admin.domain.staff.model.StaffId;
import com.inboyu.admin.domain.store.model.Store;
import com.inboyu.admin.domain.store.model.StoreId;
import com.inboyu.admin.dto.request.StaffDeptRequestDTO;
import com.inboyu.admin.dto.response.PermissionScopeDTO;
import com.inboyu.admin.dto.response.StaffMenuPermissionsResponseDTO;
import com.inboyu.admin.dto.response.StaffPermissionDTO;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Dong
 * @date 2025年08月05日 16:57
 */
@Component
public class StaffDeptAppConverter {
    public List<StaffDept> toStaffDeptList(StaffId staffId, List<StaffDeptRequestDTO> staffList) {
        if (staffList == null || staffList.isEmpty()) {
            return Collections.emptyList();
        }
        return staffList.stream()
                .map(dto -> {
                    StaffDept.StaffDeptBuilder builder = StaffDept.builder()
                            .staffId(staffId)
                            .deptId(DeptId.of(Long.valueOf(dto.getDeptId())))
                            .includeAll(dto.getIncludeAll());
                    if (dto.getStoreIds() != null) {
                        builder.storeIds(dto.getStoreIds().stream()
                                .map(storeId -> StoreId.of(Long.valueOf(storeId)))
                                .toList());
                    }
                    if (dto.getRoleIds() != null) {
                        builder.roleIds(dto.getRoleIds().stream()
                                .map(roleId -> RoleId.of(Long.valueOf(roleId)))
                                .toList());
                    }
                    if (StringUtils.isNotBlank(dto.getExpireTime())) {
                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                        builder.expireTime(LocalDateTime.parse(dto.getExpireTime(), formatter));
                    } else {
                        builder.expireTime(LocalDateTime.of(2099, 12, 31, 23, 59, 59));
                    }
                    return builder.build();
                })
                .toList();
    }

    public List<RoleId> getRoleIdList(List<StaffDept> staffRoles) {
        if (ObjectUtils.isEmpty(staffRoles)) {
            return Collections.emptyList();
        }
        return staffRoles.stream()
                .filter(staffDept -> staffDept.getRoleIds() != null)
                .flatMap(staffDept -> staffDept.getRoleIds().stream())
                .filter(Objects::nonNull)
                .distinct()
                .toList();
    }

    /**
     * 将角色权限转换为员工菜单权限响应DTO
     *
     * @param rolePermissions 角色权限列表
     * @param permissionsTree 全量权限树
     * @return {@link List}<{@link StaffMenuPermissionsResponseDTO}>
     * <AUTHOR> Dong
     * @date 2025/08/06 10:48:28
     */
    public List<StaffMenuPermissionsResponseDTO> toMenuPermissions(List<RolePermission> rolePermissions,
            MenuPermission permissionsTree) {
        if (permissionsTree == null || permissionsTree.getMenus() == null
                || permissionsTree.getMenus().isEmpty()
                || null == rolePermissions || rolePermissions.isEmpty()) {
            return Collections.emptyList();
        }

        // 提取员工拥有的菜单编码集合（只存在groupCode的情况）
        Set<String> staffMenuCodes = rolePermissions.stream()
                .filter(rolePermission -> StringUtils.isNotBlank(rolePermission.getGroupCode())
                        && (rolePermission.getPermission() == null
                                || StringUtils.isBlank(rolePermission.getPermission().getValue())))
                .map(RolePermission::getGroupCode)
                .collect(Collectors.toSet());

        // 提取员工拥有的权限编码集合（存在Permission的情况）
        Set<String> staffPermissionCodes = rolePermissions.stream()
                .filter(rolePermission -> rolePermission.getPermission() != null
                        && StringUtils.isNotBlank(rolePermission.getPermission().getValue()))
                .map(rolePermission -> rolePermission.getPermission().getValue())
                .collect(Collectors.toSet());

        // 递归处理菜单树
        return permissionsTree.getMenus().stream()
                .map(menu -> convertMenuToResponse(menu, staffMenuCodes, staffPermissionCodes))
                .filter(Objects::nonNull)
                .toList();
    }

    /**
     * 递归转换菜单为响应DTO
     *
     * @param menu                 菜单DTO
     * @param staffMenuCodes       员工菜单编码集合
     * @param staffPermissionCodes 员工权限编码集合
     * @return {@link StaffMenuPermissionsResponseDTO}
     */
    private StaffMenuPermissionsResponseDTO convertMenuToResponse(MenuPermission.MenuDTO menu,
            Set<String> staffMenuCodes,
            Set<String> staffPermissionCodes) {
        // 检查当前菜单是否在员工的菜单权限中
        boolean hasMenuPermission = staffMenuCodes.contains(menu.getCode());

        // 检查当前菜单是否有员工拥有的权限
        boolean hasPermission = menu.getPermissions() != null &&
                menu.getPermissions().stream()
                        .anyMatch(permission -> staffPermissionCodes.contains(permission.getCode()));

        // 递归处理子菜单
        List<StaffMenuPermissionsResponseDTO> children = null;
        if (menu.getChildren() != null && !menu.getChildren().isEmpty()) {
            children = menu.getChildren().stream()
                    .map(childMenu -> convertMenuToResponse(childMenu, staffMenuCodes, staffPermissionCodes))
                    .filter(Objects::nonNull)
                    .toList();
        }

        // 如果当前菜单有菜单权限、有具体权限或者子菜单有权限，则创建响应DTO
        if (hasMenuPermission || hasPermission || (children != null && !children.isEmpty())) {
            StaffMenuPermissionsResponseDTO response = new StaffMenuPermissionsResponseDTO();
            response.setCode(menu.getCode());
            response.setTitle(menu.getTitle());
            response.setIcon(menu.getIcon());
            response.setChildren(children);

            // 设置权限集合（只返回员工拥有的权限）
            if (menu.getPermissions() != null) {
                List<StaffPermissionDTO> staffPermissions = menu.getPermissions().stream()
                        .filter(permission -> staffPermissionCodes.contains(permission.getCode()))
                        .map(it -> convertPermissionToResponse(it, null))
                        .toList();
                response.setPermissions(staffPermissions);
            }
            return response;
        }
        return null;
    }

    private StaffMenuPermissionsResponseDTO convertMenuAndScopeToResponse(MenuPermission.MenuDTO menu,
            Set<String> staffMenuCodes,
            Set<String> staffPermissionCodes,
            List<String> storeIds) {
        // 检查当前菜单是否在员工的菜单权限中
        boolean hasMenuPermission = staffMenuCodes.contains(menu.getCode());

        // 检查当前菜单是否有员工拥有的权限
        boolean hasPermission = menu.getPermissions() != null &&
                menu.getPermissions().stream()
                        .anyMatch(permission -> staffPermissionCodes.contains(permission.getCode()));

        // 递归处理子菜单
        List<StaffMenuPermissionsResponseDTO> children = null;
        if (menu.getChildren() != null && !menu.getChildren().isEmpty()) {
            children = menu.getChildren().stream()
                    .map(childMenu -> convertMenuAndScopeToResponse(childMenu, staffMenuCodes, staffPermissionCodes,
                            storeIds))
                    .filter(Objects::nonNull)
                    .toList();
        }

        // 如果当前菜单有菜单权限、有具体权限或者子菜单有权限，则创建响应DTO
        if (hasMenuPermission || hasPermission || (children != null && !children.isEmpty())) {
            StaffMenuPermissionsResponseDTO response = new StaffMenuPermissionsResponseDTO();
            response.setCode(menu.getCode());
            response.setTitle(menu.getTitle());
            response.setIcon(menu.getIcon());
            response.setChildren(children);

            // 设置权限集合（只返回员工拥有的权限）
            if (menu.getPermissions() != null) {
                List<StaffPermissionDTO> staffPermissions = menu.getPermissions().stream()
                        .filter(permission -> staffPermissionCodes.contains(permission.getCode()))
                        .map(it -> {
                            PermissionScopeDTO scopeDTO = new PermissionScopeDTO();
                            scopeDTO.setStoreIds(storeIds);
                            return convertPermissionToResponse(it, scopeDTO);
                        })
                        .toList();
                response.setPermissions(staffPermissions);

            }
            return response;
        }
        return null;
    }

    /**
     * 转换权限DTO为员工权限响应DTO
     *
     * @param permissionDTO 权限DTO
     * @return {@link StaffPermissionDTO}
     */
    private StaffPermissionDTO convertPermissionToResponse(MenuPermission.PermissionDTO permissionDTO,
            PermissionScopeDTO scopeDTO) {
        StaffPermissionDTO staffPermission = new StaffPermissionDTO();
        staffPermission.setCode(permissionDTO.getCode());
        staffPermission.setTitle(permissionDTO.getTitle());
        staffPermission.setUrl(permissionDTO.getUrl());
        staffPermission.setScope(scopeDTO);
        return staffPermission;
    }

    public List<StaffMenuPermissionsResponseDTO> toMenuPermissions(List<RolePermission> rolePermissions,
            MenuPermission permissionsTree, List<StaffDept> staffDepts) {
        if (permissionsTree == null || permissionsTree.getMenus() == null
                || permissionsTree.getMenus().isEmpty()
                || null == rolePermissions || rolePermissions.isEmpty()) {
            return Collections.emptyList();
        }

        // 提取员工拥有的菜单编码集合（只存在groupCode的情况）
        Set<String> staffMenuCodes = rolePermissions.stream()
                .filter(rolePermission -> StringUtils.isNotBlank(rolePermission.getGroupCode())
                        && (rolePermission.getPermission() == null
                                || StringUtils.isBlank(rolePermission.getPermission().getValue())))
                .map(RolePermission::getGroupCode)
                .collect(Collectors.toSet());

        // 提取员工拥有的权限编码集合（存在Permission的情况）
        Set<String> staffPermissionCodes = rolePermissions.stream()
                .filter(rolePermission -> rolePermission.getPermission() != null
                        && StringUtils.isNotBlank(rolePermission.getPermission().getValue()))
                .map(rolePermission -> rolePermission.getPermission().getValue())
                .collect(Collectors.toSet());

        // 获取角色权限范围
        Map<String, List<StoreId>> roleScope = rolePermissions.stream()
                .collect(Collectors.<RolePermission, String, List<StoreId>>toMap(
                        // 显式指定key的类型为String
                        i -> i.getRoleId().getValue().toString(),
                        // 显式指定value的类型为List<StoreId>
                        i -> staffDepts.stream()
                                .filter(s -> s.getRoleIds().contains(i.getRoleId()))
                                .map(StaffDept::getStoreIds)
                                .flatMap(List::stream)
                                .collect(Collectors.toList()),
                        // 处理重复key的合并函数
                        (existing, replacement) -> {
                            existing.addAll(replacement);
                            return existing;
                        }));
        List<String> storeIds = roleScope.values().stream()
                // 将多个List<StoreId>扁平化为单个StoreId流
                .flatMap(List::stream)
                // 可选：去重操作（如果需要）
                .distinct()
                .map(i -> i.getValue().toString())
                // 收集为一个List<StoreId>
                .collect(Collectors.toList());

        // 递归处理菜单树
        return permissionsTree.getMenus().stream()
                .map(menu -> convertMenuAndScopeToResponse(menu, staffMenuCodes, staffPermissionCodes, storeIds))
                .filter(Objects::nonNull)
                .toList();
    }

    public List<StaffMenuPermissionsResponseDTO> toMenuPermissions(MenuPermission permissionsTree, List<Store> stores) {
        if (permissionsTree == null || permissionsTree.getMenus() == null
                || permissionsTree.getMenus().isEmpty()) {
            return Collections.emptyList();
        }
        System.out.println("tree =>" + JSON.toJSONString(permissionsTree));
        List<String> storeIds = stores.stream().map(s -> s.getId().getValue().toString()).distinct().toList();
        // 递归处理菜单树
        return permissionsTree.getMenus().stream()
                .map(menu -> convertMenuAndScopeToResponse(menu, storeIds))
                .filter(Objects::nonNull)
                .toList();
    }

    private StaffMenuPermissionsResponseDTO convertMenuAndScopeToResponse(MenuPermission.MenuDTO menu,
            List<String> storeIds) {
        // 递归处理子菜单
        List<StaffMenuPermissionsResponseDTO> children = null;
        if (menu.getChildren() != null && !menu.getChildren().isEmpty()) {
            children = menu.getChildren().stream()
                    .map(childMenu -> convertMenuAndScopeToResponse(childMenu, storeIds))
                    .filter(Objects::nonNull)
                    .toList();
        }

        // 检查是否有权限数据
        boolean hasPermissions = menu.getPermissions() != null && !menu.getPermissions().isEmpty();
        // 检查是否有有效子菜单
        boolean hasValidChildren = children != null && !children.isEmpty();

        // 只要有权限或有效子菜单，就创建响应DTO
        if (hasPermissions || hasValidChildren) {
            StaffMenuPermissionsResponseDTO response = new StaffMenuPermissionsResponseDTO();
            response.setCode(menu.getCode());
            response.setTitle(menu.getTitle());
            response.setIcon(menu.getIcon());
            response.setChildren(children);

            // 设置权限集合（只返回员工拥有的权限）
            if (menu.getPermissions() != null && !menu.getPermissions().isEmpty()) {
                List<StaffPermissionDTO> staffPermissions = menu.getPermissions().stream()
                        .map(it -> {
                            PermissionScopeDTO scopeDTO = new PermissionScopeDTO();
                            scopeDTO.setStoreIds(storeIds);
                            return convertPermissionToResponse(it, scopeDTO);
                        })
                        .toList();
                response.setPermissions(staffPermissions);
            }
            return response;
        }
        return null;
    }

}
