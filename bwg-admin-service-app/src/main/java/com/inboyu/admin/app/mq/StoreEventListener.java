//package com.inboyu.admin.app.mq;
//
//import com.inboyu.admin.infrastructure.event.RocketMqTopicConstant;
//import com.inboyu.admin.infrastructure.event.StoreCreateEvent;
//import com.inboyu.alimq.annotation.RocketMQTag;
//import com.inboyu.alimq.annotation.RocketMQTopic;
//
///**
// * 门店事件监听器
// *
// * <AUTHOR> Dong
// * @date 2025年08月02日 09:57
// */
//@RocketMQTopic(topic = RocketMqTopicConstant.TOPIC_BWG_TENANT)
//public class StoreEventListener {
//
//    @RocketMQTag(tag = RocketMqTopicConstant.TAG_STORE_CREATED)
//    public void addRosterAccessExecutor(StoreCreateEvent message) {
//
//    }
//}
