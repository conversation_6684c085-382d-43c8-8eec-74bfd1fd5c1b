server:
  port: 10110
  #  默认为IMMEDIATE，表示立即关机；GRACEFUL表示优雅关机
  shutdown: graceful
management:
  endpoint:
    health:
      probes:
        enabled: true
logging:
  config: ${LOGBACK_CONFIG_PATH:}
spring:
  profiles:
    active: dev
  threads:
    virtual:
      enabled: true
  # 停机过程超时时长设置了20s，超过20s，直接停机
  lifecycle:
    timeout-per-shutdown-phase: 20s
  application:
    name: admin-service-app
  datasource: # 数据库驱动
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ****************************************************************_${spring.profiles.active}_admin?allowMultiQueries=true  # 数据库连接地址
    username: bwg_op
    password: bwg@123
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
  jpa:
    show-sql: true
  data:
    redis:
      host: tenant-system-redis
      port: 6379
      password: 1eFvYJ4adRlT1D12Q2YmIPm77Spi6euH
      database: 0
      lettuce:
        pool:
          max-idle: 10
          min-idle: 10
          max-active: 20
          max-wait: 1000
  cache:
    # 缓存类型 redis、none(不使用缓存)
    type: redis
    # 缓存时间(单位：ms)
    redis:
      time-to-live: 86400000  # 1天
      # 缓存null值，防止缓存穿透
      cache-null-values: true
  jackson:
    deserialization:
      WRAP_EXCEPTIONS: false

redis:
  server:
    key-prefix: ${spring.application.name} # redis key 前缀
    enable-snow-flake-id: true #是否启用redis雪花id
    data-center-id: 2 #平台用1，商户用2
application:
  app-id: 10211
  app-key: 7c0JXAHbu9vmmW1m
  app-name: 管理服务
springdoc:
  open-api:
    info:
      title: 管理服务
  api-docs:
    enabled: true
biz:
  jpa:
    encrypt-key: 6EKo9%+Cq^yKexDD
    enable-tenant:  true #是否启用多租户hint
    dbName: bwg_${spring.profiles.active}_admin #数据库名
    #prefix: ${spring.profiles.active} #数据库前缀 组合： dev_admin_tenantId 为具体数据库到hint

aliyun:
  mq:
    accessKey: LTAI5tE9R3Ykcscnj1cQJReX
    secretKey: ******************************
    groupId: GID_BWG_ADMIN_DEV
    consumer:
      enabled: true
    nameSrvAddr: http://MQ_INST_1466901812927459_BYExAyF4.mq-internet-access.mq-internet.aliyuncs.com:80
    producer:
      enabled: true
    tagSuffix: ${spring.profiles.active}
