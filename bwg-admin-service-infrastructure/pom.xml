<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.inboyu</groupId>
		<artifactId>iby-parent</artifactId>
		<version>1.0.0-SNAPSHOT</version>
		<relativePath/> <!-- lookup parent from repository -->
	</parent>
	<artifactId>bwg-admin-service-infrastructure</artifactId>
	<version>${bwg.admin.service.version}</version>

	<dependencies>
		<dependency>
			<groupId>com.inboyu</groupId>
			<artifactId>iby-spring-cloud-starter-jpa</artifactId>
		</dependency>
		<dependency>
			<groupId>com.inboyu</groupId>
			<artifactId>iby-spring-cloud-starter-http</artifactId>
		</dependency>
		<dependency>
			<groupId>com.inboyu</groupId>
			<artifactId>iby-spring-cloud-starter-redis</artifactId>
		</dependency>
		<dependency>
			<groupId>com.inboyu</groupId>
			<artifactId>bwg-admin-service-domain</artifactId>
		</dependency>
		<dependency>
			<groupId>org.hibernate.orm</groupId>
			<artifactId>hibernate-spatial</artifactId>
		</dependency>
		<dependency>
			<groupId>org.locationtech.jts</groupId>
			<artifactId>jts-core</artifactId>
		</dependency>
		<dependency>
			<groupId>com.inboyu</groupId>
			<artifactId>bwg-admin-service-api</artifactId>
		</dependency>
		<dependency>
			<groupId>com.inboyu</groupId>
			<artifactId>iby-spring-cloud-starter-alimq</artifactId>
		</dependency>
		<dependency>
			<groupId>com.inboyu</groupId>
			<artifactId>bwg-user-center-api</artifactId>
		</dependency>
		<dependency>
			<groupId>com.inboyu</groupId>
			<artifactId>bwg-operation-center-api</artifactId>
		</dependency>
	</dependencies>

</project>
