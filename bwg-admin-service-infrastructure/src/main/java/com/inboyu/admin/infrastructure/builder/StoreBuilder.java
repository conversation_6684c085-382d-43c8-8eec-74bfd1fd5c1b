package com.inboyu.admin.infrastructure.builder;

import com.inboyu.admin.domain.store.model.GeoPoint;
import com.inboyu.admin.domain.store.model.Store;
import com.inboyu.admin.event.StoreCreateEvent;
import com.inboyu.admin.infrastructure.dao.entity.StoreEntity;
import org.locationtech.jts.geom.Point;

/**
 * <AUTHOR>
 * @date 2025年08月02日 14:52
 */
public interface StoreBuilder {
    StoreEntity toStoreEntity(Store store);

    Point toPoint(GeoPoint geoPoint);

    GeoPoint toGeoPoint(Point point);

    Store toStore(StoreEntity storeEntity);

    StoreCreateEvent toCreateEvent(Store store);

    StoreEntity toUpdateStoreEntity(Store store, StoreEntity existingEntity);
}
