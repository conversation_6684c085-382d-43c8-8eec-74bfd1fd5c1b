package com.inboyu.admin.infrastructure.dao;

import com.inboyu.admin.infrastructure.dao.entity.RoleEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface <PERSON><PERSON><PERSON> extends JpaRepository<RoleEntity, Long> {

    /**
     * 通过角色名称统计角色数量
     *
     * @param title
     * @param deleted
     * @return int
     * <AUTHOR> Dong
     * @date 2025/07/31 15:05:14
     */
    int countByTitleAndDeleted(String title, int deleted);

    /**
     * 通过角色id查询角色
     *
     * @param roleId
     * @param deleted
     * @return {@link RoleEntity }
     * <AUTHOR>
     * @date 2025/07/31 17:00:54
     */
    RoleEntity findByRoleIdAndDeleted(Long roleId, int deleted);

    /**
     * 通过角色id+是否可见查询角色
     *
     * @param value
     * @param enabled
     * @param deleted
     * @return {@link RoleEntity }
     * <AUTHOR>
     * @date 2025/07/31 19:17:31
     */
    RoleEntity findByRoleIdAndEnabledAndDeleted(Long value, Integer enabled, int deleted);

    /**
     * 根据角色名称查询角色数量（排除指定角色）
     *
     * @param title
     * @param deleted
     * @param roleId
     * @return int
     * <AUTHOR> Dong
     * @date 2025/07/31 20:10:20
     */
    int countByTitleAndDeletedAndRoleIdNot(String title, int deleted, Long roleId);

    @Transactional
    @Modifying
    @Query("update RoleEntity set title = :#{#entity.title}, remark = :#{#entity.remark}, status = :#{#entity.status}, enabled = :#{#entity.enabled}, version = version + 1 where roleId = :#{#entity.roleId}")
    void update(RoleEntity entity);

    /**
     * 根据角色名称模糊查询
     *
     * @param status
     * @param title
     * @param deleted
     * @return {@link List }<{@link RoleEntity }>
     * <AUTHOR> Dong
     * @date 2025/07/31 21:10:54
     */
    @Query("SELECT r FROM RoleEntity r WHERE r.deleted = :deleted AND (:status IS NULL OR r.status = :status) AND (:title IS NULL OR r.title LIKE %:title%) ORDER BY r.createTime desc")
    List<RoleEntity> findByStatusAndTitleLikeAndDeletedOrderByCreateTimeDesc(String status, String title, int deleted);

    /**
     * 根据删除标识查询
     *
     * @param notDeleted
     * @return {@link List }<{@link RoleEntity }>
     * <AUTHOR> Dong
     * @date 2025/08/01 10:17:06
     */
    List<RoleEntity> findByDeleted(int notDeleted);

    /**
     * 根据角色ID列表批量查询角色
     *
     * @param roleIds 角色ID列表
     * @param deleted 删除标识
     * @return {@link List }<{@link RoleEntity }>
     * <AUTHOR> Dong
     * @date 2025/08/05 12:00:00
     */
    List<RoleEntity> findByRoleIdInAndDeleted(List<Long> roleIds, Integer deleted);
}
