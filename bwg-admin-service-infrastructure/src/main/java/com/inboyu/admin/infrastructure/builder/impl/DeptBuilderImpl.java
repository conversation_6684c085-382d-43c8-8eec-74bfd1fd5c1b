package com.inboyu.admin.infrastructure.builder.impl;

import com.inboyu.admin.domain.dept.model.Dept;
import com.inboyu.admin.domain.dept.model.DeptId;
import com.inboyu.admin.infrastructure.builder.DeptBuilder;
import com.inboyu.admin.infrastructure.dao.entity.DeptEntity;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * <AUTHOR> Dong
 * @date 2025年07月29日 15:33
 */
@Component
public class DeptBuilderImpl implements DeptBuilder {
    public DeptEntity toDeptEntity(Dept dept) {
        DeptEntity entity = new DeptEntity();
        entity.setDeptId(dept.getId().getValue());
        entity.setParentDeptId(dept.getParentDeptId().getValue());
        entity.setTitle(dept.getTitle());
        entity.setDeleted(0);
        entity.setCreateTime(LocalDateTime.now());
        entity.setModifyTime(LocalDateTime.now());
        return entity;
    }

    public Dept toDept(DeptEntity entity) {
        if (null == entity) {
            return null;
        }
        return Dept.builder().id(DeptId.of(entity.getDeptId()))
                .parentDeptId(DeptId.of(entity.getParentDeptId())).title(entity.getTitle()).build();
    }
}
