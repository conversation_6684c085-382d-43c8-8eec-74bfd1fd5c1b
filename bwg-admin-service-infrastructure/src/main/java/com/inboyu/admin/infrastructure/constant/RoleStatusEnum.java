package com.inboyu.admin.infrastructure.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 角色状态枚举
 *
 * <AUTHOR> Dong
 * @date 2025年08月06日 19:35
 */
@AllArgsConstructor
@Getter
public enum RoleStatusEnum {
    ENABLED("status.enabled", "启用"),
    DISABLED("status.disabled", "停用");
    private final String code;
    private final String title;

    /**
     * @description: 根据code获取title
     * @author: z<PERSON><PERSON>
     * @date: 2025/8/5 15:28
     * @param: [code]
     * @return: java.lang.String
     **/
    public static String titleFromCode(String code) {
        for (StaffStatusEnum status : StaffStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status.getTitle();
            }
        }
        return null;
    }
}
