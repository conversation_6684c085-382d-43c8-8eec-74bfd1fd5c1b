package com.inboyu.admin.infrastructure.repository;

import com.inboyu.admin.domain.dept.model.DeptId;
import com.inboyu.admin.domain.store.model.Store;
import com.inboyu.admin.domain.store.model.StoreId;
import com.inboyu.admin.domain.store.repository.StoreRepository;
import com.inboyu.admin.infrastructure.builder.StoreBuilder;
import com.inboyu.admin.infrastructure.dao.StoreDao;
import com.inboyu.admin.infrastructure.dao.entity.StoreEntity;
import com.inboyu.constant.DeleteFlag;
import com.inboyu.spring.cloud.starter.common.dto.Pagination;
import com.inboyu.spring.cloud.starter.redis.utils.id.RedisSnowflakeIdGenerator;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年08月02日 09:44
 */
@Repository
public class StoreRepositoryImpl implements StoreRepository {
    @Autowired
    private StoreDao storeDao;

    @Autowired
    private RedisSnowflakeIdGenerator snowflakeIdGenerator;

    @Autowired
    private StoreBuilder storeBuilder;

    /**
     * 生成门店id
     *
     * @return {@link StoreId }
     * <AUTHOR> Dong
     * @date 2025/08/02 14:38:46
     */
    @Override
    public StoreId generateId() {
        return StoreId.of(snowflakeIdGenerator.nextId());
    }

    /**
     * 根据门店名称统计门店数量
     *
     * @param title
     * @return int
     * <AUTHOR> Dong
     * @date 2025/08/02 14:49:45
     */
    @Override
    public int countByTitle(String title) {
        return storeDao.countByTitleAndDeleted(title, DeleteFlag.NOT_DELETED);
    }

    /**
     * 根据门店名称和门店ID统计门店数量（用于更新时排除自己）
     *
     * @param title
     * @param storeId
     * @return int
     * <AUTHOR> Dong
     * @date 2025/08/02 16:00
     */
    @Override
    public int countByTitleAndStoreIdNot(String title, StoreId storeId) {
        return storeDao.countByTitleAndStoreIdNotAndDeleted(title, storeId.getValue(), DeleteFlag.NOT_DELETED);
    }

    /**
     * 保存门店
     *
     * @param store
     * @return {@link Store }
     * <AUTHOR> Dong
     * @date 2025/08/02 14:49:59
     */
    @Override
    public Store save(Store store) {
        StoreEntity entity = storeBuilder.toStoreEntity(store);
        StoreEntity storeEntity = storeDao.save(entity);
        return storeBuilder.toStore(storeEntity);
    }

    /**
     * 根据门店ID查询门店
     *
     * @param storeId
     * @return {@link Store }
     * <AUTHOR> Dong
     * @date 2025/08/02 16:00
     */
    @Override
    public Store findByStoreId(StoreId storeId) {
        StoreEntity entity = storeDao.findByStoreIdAndDeleted(storeId.getValue(), DeleteFlag.NOT_DELETED);
        if (entity == null) {
            return null;
        }
        return storeBuilder.toStore(entity);
    }

    /**
     * 更新门店
     *
     * @param store
     * <AUTHOR> Dong
     * @date 2025/08/02 16:00
     */
    @Override
    @Transactional
    public void update(Store store) {
        // 1. 转换为实体对象
        StoreEntity storeEntity = storeBuilder.toStoreEntity(store);
        // 2. 执行更新操作
        storeDao.updateStore(store.getId().getValue(), storeEntity);
    }

    // /**
    // * 发送门店创建消息
    // *
    // * @param store
    // * <AUTHOR> Dong
    // * @date 2025/08/02 15:51:46
    // */
    // @Override
    // public void sendCreateEvent(Store store) {
    // StoreCreateEvent event = storeBuilder.toCreateEvent(store);
    //// rocketMQTemplate.send(event);
    // }

    // /**
    // * 发送门店更新消息
    // *
    // * @param store
    // * <AUTHOR> Dong
    // * @date 2025/08/02 16:30
    // */
    // @Override
    // public void sendUpdateEvent(Store store) {
    // StoreCreateEvent event = storeBuilder.toCreateEvent(store);
    //// rocketMQTemplate.send(event);
    // }

    /**
     * 分页查询门店
     *
     * @param pageNum  当前页
     * @param pageSize 每页条数
     * @param keywords 门店名称关键词(支持模糊)
     * @param deptIds
     * @return {@link com.inboyu.spring.cloud.starter.common.dto.Pagination }<{@link Store
     *         }>
     * <AUTHOR> Dong
     * @date 2025/08/04 11:40:33
     */
    @Override
    public Pagination<Store> pageStores(Integer pageNum, Integer pageSize, String keywords, List<DeptId> deptIds) {
        // 创建分页参数
        Pageable pageable = PageRequest.of(pageNum - 1, pageSize);
        List<Long> deptIdList = null;
        if (ObjectUtils.isNotEmpty(deptIds)) {
            deptIdList = deptIds.stream().map(DeptId::getValue).toList();
        }
        // 查询数据（支持多组织ID）
        Page<StoreEntity> entityPage = storeDao.pageByTitleLikeAndDeptIds(deptIdList, keywords, pageable);

        // 转换为领域模型
        List<Store> stores = entityPage.getContent().stream()
                .map(storeBuilder::toStore)
                .toList();

        // 构造分页结果
        return new Pagination<>(
                entityPage.getNumber() + 1,
                entityPage.getSize(),
                entityPage.getTotalPages(),
                entityPage.getTotalElements(),
                stores);
    }

    /**
     * 批量查询组织下门店数
     *
     * @param deptIds
     * @return {@link Integer }
     * <AUTHOR> Dong
     * @date 2025/08/04 17:57:39
     */
    @Override
    public Integer countByDeptIds(List<DeptId> deptIds) {
        return storeDao.countByDeptIdInAndDeleted(DeptId.ofList(deptIds), DeleteFlag.NOT_DELETED);
    }

    /**
     * 根据门店ID列表批量查询门店
     *
     * @param storeIds 门店ID列表
     * @return {@link List }<{@link Store }>
     * <AUTHOR> Dong
     * @date 2025/08/05 12:00:00
     */
    @Override
    public List<Store> findByStoreIds(List<StoreId> storeIds) {
        if (storeIds == null || storeIds.isEmpty()) {
            return List.of();
        }
        List<Long> storeIdValues = storeIds.stream().map(StoreId::getValue).toList();
        List<StoreEntity> entities = storeDao.findByStoreIdInAndDeleted(storeIdValues, DeleteFlag.NOT_DELETED);
        return entities.stream()
                .map(storeBuilder::toStore)
                .toList();
    }

    @Override
    public List<Store> findByDeptIds(List<DeptId> deptIds) {
        List<StoreEntity> entities = storeDao.findByDeptIdInAndDeleted(DeptId.ofList(deptIds), DeleteFlag.NOT_DELETED);
        return entities.stream()
                .map(storeBuilder::toStore)
                .toList();
    }

    @Override
    public List<Store> findAll() {
        List<StoreEntity> entities = storeDao.findAll();
        return entities.stream()
                .map(storeBuilder::toStore)
                .toList();
    }
}
