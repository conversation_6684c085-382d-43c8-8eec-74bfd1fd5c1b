package com.inboyu.admin.infrastructure.dao;

import com.inboyu.admin.infrastructure.dao.entity.StaffEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

public interface StaffDao extends JpaRepository<StaffEntity, Long> {

    /**
     * @description: 根据staffId和deleted查询员工信息
     * @author: zhouxin
     * @date: 2025/8/5 15:16
     * @param: [staffId, deleted] 员工id，是否删除
     * @return: com.inboyu.admin.infrastructure.dao.entity.StaffEntity
     **/
    StaffEntity findByStaffIdAndDeleted(Long staffId, int deleted);

    /**
     * 根据手机号统计
     *
     * @param phone
     * @param deleted
     * @return int
     * <AUTHOR> Dong
     * @date 2025/08/05 15:21:51
     */
    int countByPhoneAndDeleted(String phone, int deleted);

    int countByPhoneAndDeletedAndStaffIdNot(String phone, int deleted, Long staffId);

    @Transactional
    @Modifying
    @Query("update StaffEntity set userId = :#{#entity.userId}, phone = :#{#entity.phone}, name = :#{#entity.name}, status = :#{#entity.status}, version = version + 1 where staffId = :#{#entity.staffId}")
    void update(StaffEntity entity);
}
