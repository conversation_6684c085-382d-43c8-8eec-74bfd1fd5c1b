package com.inboyu.admin.infrastructure.dao.entity;

import com.inboyu.spring.cloud.starter.jpa.annotation.Encrypted;
import com.inboyu.spring.cloud.starter.jpa.converter.SensitiveConverter;
import com.inboyu.spring.cloud.starter.jpa.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "t_staff")
public class StaffEntity extends BaseEntity {
    @Column(name = "staff_id", nullable = false)
    private Long staffId;

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Encrypted
    @Convert(converter = SensitiveConverter.class)
    @Column(name = "name", nullable = false, length = 50)
    private String name;

    @Encrypted
    @Convert(converter = SensitiveConverter.class)
    @Column(name = "phone", nullable = false, length = 50)
    private String phone;

    @Column(name = "status", nullable = false, length = 50)
    private String status;

}
