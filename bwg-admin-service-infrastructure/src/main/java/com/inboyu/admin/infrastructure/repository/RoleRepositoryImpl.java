package com.inboyu.admin.infrastructure.repository;

import com.inboyu.admin.domain.role.model.Role;
import com.inboyu.admin.domain.role.model.RoleId;
import com.inboyu.admin.domain.role.repository.RoleRepository;
import com.inboyu.admin.infrastructure.builder.RoleBuilder;
import com.inboyu.admin.infrastructure.dao.RoleDao;
import com.inboyu.admin.infrastructure.dao.entity.RoleEntity;
import com.inboyu.constant.DeleteFlag;
import com.inboyu.spring.cloud.starter.redis.utils.id.RedisSnowflakeIdGenerator;
import com.inboyu.util.convert.BooleanIntegerConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class RoleRepositoryImpl implements RoleRepository {

    @Autowired
    private RoleDao roleDao;

    @Autowired
    private RoleBuilder roleBuilder;

    @Autowired
    private RedisSnowflakeIdGenerator snowflakeIdGenerator;

    @Override
    public Role findByRoleId(RoleId roleId) {
        RoleEntity roleEntity = roleDao.findByRoleIdAndDeleted(roleId.getValue(), DeleteFlag.NOT_DELETED);
        return roleBuilder.toRole(roleEntity);
    }

    @Override
    public Role findByRoleIdAndEnabled(RoleId roleId, Boolean enabled) {
        RoleEntity roleEntity = roleDao.findByRoleIdAndEnabledAndDeleted(roleId.getValue(), BooleanIntegerConverter.booleanToInt(enabled), DeleteFlag.NOT_DELETED);
        return roleBuilder.toRole(roleEntity);
    }

    /**
     * 通过角色名称统计角色数量
     *
     * @param title
     * @return {@link Role }
     * <AUTHOR> Dong
     * @date 2025/07/31 14:59:39
     */
    @Override
    public int countByTitle(String title) {
        return roleDao.countByTitleAndDeleted(title, DeleteFlag.NOT_DELETED);
    }

    /**
     * 生成角色id
     *
     * @return {@link RoleId }
     * <AUTHOR> Dong
     * @date 2025/07/31 15:11:06
     */
    @Override
    public RoleId generateId() {
        return RoleId.of(snowflakeIdGenerator.nextId());
    }

    /**
     * 保存角色
     *
     * @param role
     * @return {@link Role }
     * <AUTHOR> Dong
     * @date 2025/07/31 15:16:46
     */
    @Override
    public Role save(Role role) {
        RoleEntity roleEntity = roleBuilder.toNewEntity(role);
        RoleEntity saveEntity = roleDao.save(roleEntity);
        return roleBuilder.toRole(saveEntity);
    }

    /**
     * 根据角色名称查询角色数量（排除指定角色）
     *
     * @param title
     * @param id
     * @return int
     * <AUTHOR> Dong
     * @date 2025/07/31 20:04:43
     */
    @Override
    public int countByTitleAndRoleIdNot(String title, RoleId id) {
        return roleDao.countByTitleAndDeletedAndRoleIdNot(title, DeleteFlag.NOT_DELETED, id.getValue());
    }

    /**
     * 更新角色
     *
     * @param role
     * <AUTHOR> Dong
     * @date 2025/07/31 20:14:16
     */
    @Override
    public void updateRole(Role role) {
        RoleEntity entity = roleBuilder.toUpdateEntity(role);
        roleDao.update(entity);
    }

    /**
     * 通过角色名称查询角色列表(支持模糊)
     *
     * @param title
     * @param status
     * @return {@link List }<{@link Role }>
     * <AUTHOR> Dong
     * @date 2025/07/31 21:01:20
     */
    @Override
    public List<Role> findRolesByStatusAndTitleLike(String status, String title) {
        List<RoleEntity> roles = roleDao.findByStatusAndTitleLikeAndDeletedOrderByCreateTimeDesc(status, title, DeleteFlag.NOT_DELETED);
        return roleBuilder.toRoles(roles);
    }

    /**
     * 查询所有角色
     *
     * @return {@link List }<{@link Role }>
     * <AUTHOR> Dong
     * @date 2025/08/01 10:15:03
     */
    @Override
    public List<Role> findAll() {
        List<RoleEntity> roles = roleDao.findByDeleted(DeleteFlag.NOT_DELETED);
        return roleBuilder.toRoles(roles);
    }

    /**
     * 根据角色ID列表批量查询角色
     *
     * @param roleIds 角色ID列表
     * @return {@link List }<{@link Role }>
     * <AUTHOR> Dong
     * @date 2025/08/05 12:00:00
     */
    @Override
    public List<Role> findByRoleIds(List<RoleId> roleIds) {
        if (roleIds == null || roleIds.isEmpty()) {
            return List.of();
        }
        List<Long> roleIdValues = roleIds.stream().map(RoleId::getValue).toList();
        List<RoleEntity> entities = roleDao.findByRoleIdInAndDeleted(roleIdValues, DeleteFlag.NOT_DELETED);
        return entities.stream()
                .map(roleBuilder::toRole)
                .toList();
    }
}
