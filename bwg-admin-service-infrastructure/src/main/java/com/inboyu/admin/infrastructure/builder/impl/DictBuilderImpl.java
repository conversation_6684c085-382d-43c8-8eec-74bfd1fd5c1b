package com.inboyu.admin.infrastructure.builder.impl;

import com.inboyu.admin.domain.dict.model.Dict;
import com.inboyu.admin.domain.dict.model.DictCode;
import com.inboyu.admin.infrastructure.builder.DictBuilder;
import com.inboyu.admin.infrastructure.dao.entity.DictEntity;
import com.inboyu.constant.DeleteFlag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Dong
 * @date 2025年08月04日 10:20
 */
@Component
public class DictBuilderImpl implements DictBuilder {
    @Override
    public Dict toDict(DictEntity entity) {
        if (null == entity) {
            return null;
        }
        return Dict.builder()
                .code(DictCode.of(entity.getCode()))
                .type(DictCode.of(entity.getType()))
                .title(entity.getTitle())
                .icon(entity.getIcon())
                .remark(entity.getRemark())
                .index(entity.getIndex()).build();
    }

    @Override
    public DictEntity toNewEntity(Dict dict) {
        DictEntity dictEntity = new DictEntity();
        dictEntity.setCode(dict.getCode().getValue());
        dictEntity.setType(dict.getType().getValue());
        dictEntity.setTitle(dict.getTitle());
        dictEntity.setIcon(dict.getIcon());
        if (StringUtils.isBlank(dict.getRemark())) {
            dictEntity.setRemark("");
        } else {
            dictEntity.setRemark(dict.getRemark());
        }
        dictEntity.setIndex(dict.getIndex());
        dictEntity.setDeleted(DeleteFlag.NOT_DELETED);
        dictEntity.setCreateTime(LocalDateTime.now());
        dictEntity.setModifyTime(LocalDateTime.now());
        return dictEntity;
    }

    @Override
    public DictEntity toUpdateEntity(Dict dict) {
        DictEntity dictEntity = new DictEntity();
        dictEntity.setCode(dict.getCode().getValue());
        dictEntity.setType(dict.getType().getValue());
        dictEntity.setTitle(dict.getTitle());
        dictEntity.setIcon(dict.getIcon());
        if (StringUtils.isBlank(dict.getRemark())) {
            dictEntity.setRemark("");
        } else {
            dictEntity.setRemark(dict.getRemark());
        }
        dictEntity.setIndex(dict.getIndex());
        dictEntity.setDeleted(DeleteFlag.NOT_DELETED);
        dictEntity.setCreateTime(LocalDateTime.now());
        dictEntity.setModifyTime(LocalDateTime.now());
        return dictEntity;
    }

    @Override
    public List<Dict> toDicts(List<DictEntity> entities) {
        return entities.stream().map(this::toDict).collect(Collectors.toList());
    }
}
