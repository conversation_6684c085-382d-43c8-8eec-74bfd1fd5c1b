package com.inboyu.admin.infrastructure.repository;

import com.inboyu.admin.domain.dept.model.Dept;
import com.inboyu.admin.domain.dept.model.DeptId;
import com.inboyu.admin.domain.dept.repository.DeptRepository;
import com.inboyu.admin.infrastructure.builder.DeptBuilder;
import com.inboyu.admin.infrastructure.dao.DeptDao;
import com.inboyu.admin.infrastructure.dao.entity.DeptEntity;
import com.inboyu.constant.DeleteFlag;
import com.inboyu.spring.cloud.starter.redis.utils.id.RedisSnowflakeIdGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年07月29日 14:23
 */
@Repository
public class DeptRepositoryImpl implements DeptRepository {
    @Autowired
    private DeptDao deptDao;

    @Autowired
    private DeptBuilder deptBuilder;

    @Autowired
    private RedisSnowflakeIdGenerator snowflakeIdGenerator;

    /**
     * 生成部门ID
     *
     * @return DeptId
     * <AUTHOR> Dong
     * @date 2025/07/30 10:43:47
     */
    @Override
    public DeptId generateId() {
        return DeptId.of(snowflakeIdGenerator.nextId());
    }

    /**
     * @param dept
     * @return {@link Dept }
     * <AUTHOR> Dong
     * @date 2025/07/29 15:40:03
     */
    public Dept saveDept(Dept dept) {
        DeptEntity entity = deptDao.save(deptBuilder.toDeptEntity(dept));
        return deptBuilder.toDept(entity);
    }

    /**
     * 更新部门
     *
     * @param dept
     * @return {@link Dept }
     * <AUTHOR> Dong
     * @date 2025/07/31 09:50:00
     */
    @Override
    public void updateDeptTitle(Dept dept) {
        DeptEntity entity = deptDao.findByDeptIdAndDeleted(dept.getId().getValue(), DeleteFlag.NOT_DELETED);
        entity.setTitle(dept.getTitle());
        deptDao.save(entity);
    }

    /**
     * 根据组织id查询组织
     *
     * @param deptId
     * @return {@link Dept }
     * <AUTHOR> Dong
     * @date 2025/07/29 15:26:49
     */
    @Override
    public Dept findByDeptId(DeptId deptId) {
        DeptEntity entity = deptDao.findByDeptIdAndDeleted(deptId.getValue(), DeleteFlag.NOT_DELETED);
        return deptBuilder.toDept(entity);
    }

    /**
     * 根据上级组织id+组织名称查询组织数量
     *
     * @param deptId
     * @param title
     * @return int
     * <AUTHOR> Dong
     * @date 2025/07/29 20:30:14
     */
    @Override
    public int countByParentDeptIdAndTitle(DeptId deptId, String title) {
        return deptDao.countByParentDeptIdAndTitleAndDeleted(deptId.getValue(), title, DeleteFlag.NOT_DELETED);
    }

    /**
     * 根据上级组织id+组织名称查询组织数量（排除指定组织）
     *
     * @param parentDeptId
     * @param title
     * @param deptId
     * @return int
     * <AUTHOR> Dong
     * @date 2025/07/31 09:50:00
     */
    @Override
    public int countByParentDeptIdAndTitleAndDeptIdNot(DeptId parentDeptId, String title, DeptId deptId) {
        return deptDao.countByParentDeptIdAndTitleAndDeletedAndDeptIdNot(parentDeptId.getValue(), title, DeleteFlag.NOT_DELETED, deptId.getValue());
    }

    /**
     * 查询父组织下的所有子组织
     *
     * @param deptId
     * @return {@link List }<{@link Dept }>
     * <AUTHOR> Dong
     * @date 2025/08/04 15:55:23
     */
    @Override
    public List<Dept> findAllChildDept(DeptId deptId) {
        List<DeptEntity> all = new ArrayList<>();
        List<Long> work = new ArrayList<>();
        work.add(deptId.getValue());
        for (int i = 0; i < work.size(); i++) {
            Long pid = work.get(i);
            List<DeptEntity> children = deptDao.findByParentDeptIdAndDeleted(pid, DeleteFlag.NOT_DELETED);
            if (children == null || children.isEmpty()) {
                continue;
            }
            all.addAll(children);
            for (DeptEntity child : children) {
                work.add(child.getDeptId());
            }
        }
        return all.stream().map(deptBuilder::toDept).toList();
    }

    /**
     * 查询所有组织
     *
     * @return {@link List <Dept> }
     * <AUTHOR> Dong
     * @date 2025/07/30 14:30:00
     */
    @Override
    public List<Dept> findAllDept() {
        List<DeptEntity> entities = deptDao.findByDeleted(DeleteFlag.NOT_DELETED);
        return entities.stream()
                .map(deptBuilder::toDept)
                .toList();
    }

    /**
     * 删除部门
     *
     * @param deptId
     * @return int
     * <AUTHOR> Dong
     * @date 2025/08/04 15:55:23
     */
    @Override
    public int deleteDept(DeptId deptId) {
        return deptDao.deleteByDeptId(deptId.getValue(), DeleteFlag.DELETED);
    }

    /**
     * 根据部门ID列表批量查询部门
     *
     * @param deptIds 部门ID列表
     * @return {@link List }<{@link Dept }>
     * <AUTHOR> Dong
     * @date 2025/08/05 12:00:00
     */
    @Override
    public List<Dept> findByDeptIds(List<DeptId> deptIds) {
        if (deptIds == null || deptIds.isEmpty()) {
            return List.of();
        }
        List<Long> deptIdValues = deptIds.stream().map(DeptId::getValue).toList();
        List<DeptEntity> entities = deptDao.findByDeptIdInAndDeleted(deptIdValues, DeleteFlag.NOT_DELETED);
        return entities.stream()
                .map(deptBuilder::toDept)
                .toList();
    }

    /**
     * 批量删除部门
     *
     * @param deptIds 部门ID列表
     * @return int
     * <AUTHOR> Dong
     * @date 2025/08/05 12:00:00
     */
    @Override
    public int deleteDepts(List<DeptId> deptIds) {
        if (deptIds == null || deptIds.isEmpty()) {
            return 0;
        }
        List<Long> deptIdValues = deptIds.stream().map(DeptId::getValue).toList();
        return deptDao.deleteByDeptIdIn(deptIdValues, DeleteFlag.DELETED);
    }
}
