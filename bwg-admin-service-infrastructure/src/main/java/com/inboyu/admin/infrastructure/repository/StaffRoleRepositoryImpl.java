package com.inboyu.admin.infrastructure.repository;

import com.inboyu.admin.domain.dept.model.DeptId;
import com.inboyu.admin.domain.staff.model.StaffDept;
import com.inboyu.admin.domain.staff.model.StaffId;
import com.inboyu.admin.domain.staff.repository.StaffRoleRepository;
import com.inboyu.admin.infrastructure.builder.StaffRoleBuilder;
import com.inboyu.admin.infrastructure.dao.StaffRoleDao;
import com.inboyu.admin.infrastructure.dao.entity.StaffRoleEntity;
import com.inboyu.constant.DeleteFlag;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.criteria.*;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.locationtech.jts.util.CollectionUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年08月05日 15:53
 */
@Repository
public class StaffRoleRepositoryImpl implements StaffRoleRepository {
    @Autowired
    private StaffRoleDao staffRoleDao;
    @Autowired
    private StaffRoleBuilder staffRoleBuilder;

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public void saveAll(List<StaffDept> staffDeptList) {
        List<StaffRoleEntity> staffRoles = staffRoleBuilder.toEntities(staffDeptList);
        staffRoleDao.saveAll(staffRoles);
    }

    @Override
    public List<StaffDept> queryByStaffId(StaffId staffId) {
        List<StaffRoleEntity> entities = staffRoleDao.findByStaffIdAndDeleted(staffId.getValue(), DeleteFlag.NOT_DELETED);
        return staffRoleBuilder.toStaffDeptList(entities);
    }

    @Override
    public Integer countByDeptIds(List<DeptId> deptIds) {
        List<Long> deptIdList = deptIds.stream().map(DeptId::getValue).toList();
        return staffRoleDao.countByDeptIdInAndDeleted(deptIdList, DeleteFlag.NOT_DELETED);
    }

    @Override
    public void deleteByStaffId(StaffId staffId) {
        staffRoleDao.deleteByStaffId(staffId.getValue());
    }

    @Override
    public List<StaffDept> listByRoleIdsAndDeptIds(List<String> roleIds, List<Long> deptIds) {

        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<StaffRoleEntity> query = cb.createQuery(StaffRoleEntity.class);
        Root<StaffRoleEntity> root = query.from(StaffRoleEntity.class);

        // 构建条件列表
        List<Predicate> predicates = new ArrayList<>();

        // 1. 固定条件：未删除
        predicates.add(cb.equal(root.get("deleted"), DeleteFlag.NOT_DELETED));

        // 2. 动态处理roleIds条件
        List<Predicate> rolePredicates = new ArrayList<>();
        if (!CollectionUtils.isEmpty(roleIds)) {
            // 为每个角色ID构建匹配条件
            for (String roleId : roleIds) {
                // 安全处理：添加逗号分隔符确保精确匹配
                String pattern = "%," + roleId + ",%";
                Expression<String> roleIdsExpr = root.get("roleIds");

                // 处理边界情况：开头或结尾的roleId
                Predicate middleMatch = cb.like(roleIdsExpr, pattern);
                Predicate startMatch = cb.like(roleIdsExpr, roleId + ",%");
                Predicate endMatch = cb.like(roleIdsExpr, "%," + roleId);
                Predicate exactMatch = cb.equal(roleIdsExpr, roleId);

                // 组合单个角色ID的所有匹配情况
                rolePredicates.add(cb.or(middleMatch, startMatch, endMatch, exactMatch));
            }
        }

        // 将所有角色ID的条件用OR组合
        if (!rolePredicates.isEmpty()) {
            Predicate combinedRolePredicate = cb.or(rolePredicates.toArray(new Predicate[0]));
            predicates.add(combinedRolePredicate);
        }

        // 3. 动态处理deptIds条件
        if (!CollectionUtils.isEmpty(deptIds)) {
            // 如果deptIds不为空，则添加该条件
            predicates.add(root.get("deptId").in(deptIds));
        }

        // 应用所有条件
        query.where(cb.and(predicates.toArray(new Predicate[0])));

        // 执行查询
        List<StaffRoleEntity> entities = entityManager.createQuery(query).getResultList();
        if (CollectionUtils.isEmpty(entities)) {
            return Collections.emptyList();
        }
        return staffRoleBuilder.toStaffDeptList(entities);
    }

}
