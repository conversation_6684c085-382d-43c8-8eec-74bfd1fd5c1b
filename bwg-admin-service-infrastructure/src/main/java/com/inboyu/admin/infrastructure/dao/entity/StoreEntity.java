package com.inboyu.admin.infrastructure.dao.entity;

import com.inboyu.spring.cloud.starter.jpa.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.locationtech.jts.geom.Point;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "t_store")
public class StoreEntity extends BaseEntity {

    @Column(name = "store_id", nullable = false)
    private Long storeId;

    @Column(name = "dept_id", nullable = false)
    private Long deptId;

    @Column(name = "title", nullable = false, length = 50)
    private String title;

    @Column(name = "province_code", nullable = false, length = 10)
    private String provinceCode;

    @Column(name = "province_name", nullable = false, length = 50)
    private String provinceName;

    @Column(name = "city_code", nullable = false, length = 10)
    private String cityCode;

    @Column(name = "city_name", nullable = false, length = 50)
    private String cityName;

    @Column(name = "county_code", nullable = false, length = 10)
    private String countyCode;

    @Column(name = "county_name", nullable = false, length = 50)
    private String countyName;

    @Column(name = "address", nullable = false, length = 500)
    private String address;

    @Column(name = "coordinate", nullable = false)
    private Point coordinate;
}
