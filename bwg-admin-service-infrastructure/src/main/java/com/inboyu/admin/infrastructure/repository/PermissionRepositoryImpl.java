package com.inboyu.admin.infrastructure.repository;

import com.inboyu.admin.domain.permission.model.MenuPermission;
import com.inboyu.admin.domain.permission.repository.PermissionRepository;
import com.inboyu.admin.infrastructure.builder.PermissionBuilder;
import com.inboyu.operation.api.PermissionFeignClient;
import com.inboyu.operation.dto.response.MenuPermissionResponseDTO;
import com.inboyu.spring.cloud.starter.http.api.container.FeignAccessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2025年08月05日 21:09
 */
@Repository
public class PermissionRepositoryImpl implements PermissionRepository {

    @Autowired
    private FeignAccessor feignAccessor;

    @Autowired
    private PermissionBuilder permissionBuilder;

    @Override
    public MenuPermission getPermissionsTree() {
        PermissionFeignClient client = feignAccessor.get(PermissionFeignClient.class);
        MenuPermissionResponseDTO dto = client.getPermissionsTree();
        return permissionBuilder.convertMenuDTO(dto);
    }
}
