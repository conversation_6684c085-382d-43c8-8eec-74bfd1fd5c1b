package com.inboyu.admin.infrastructure.builder.impl;

import com.inboyu.admin.domain.permission.model.MenuPermission;
import com.inboyu.admin.infrastructure.builder.PermissionBuilder;
import com.inboyu.operation.dto.response.MenuPermissionResponseDTO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年08月06日 11:28
 */
@Component
public class PermissionBuilderImpl implements PermissionBuilder {
    @Override
    public MenuPermission convertMenuDTO(MenuPermissionResponseDTO dto) {
        // 手动转换，避免类型转换错误
        MenuPermission menuPermission = new MenuPermission();
        if (dto != null && dto.getMenus() != null) {
            List<MenuPermission.MenuDTO> menuDTOs = dto.getMenus().stream()
                    .map(this::convertMenuDTO)
                    .toList();
            menuPermission.setMenus(menuDTOs);
        }
        return menuPermission;
    }

    /**
     * 转换菜单DTO
     */
    private MenuPermission.MenuDTO convertMenuDTO(MenuPermissionResponseDTO.MenuDTO source) {
        MenuPermission.MenuDTO target = new MenuPermission.MenuDTO();
        target.setCode(source.getCode());
        target.setTitle(source.getTitle());
        target.setIcon(source.getIcon());
        // 转换权限列表
        if (source.getPermissions() != null) {
            List<MenuPermission.PermissionDTO> permissions = source.getPermissions().stream()
                    .map(this::convertPermissionDTO)
                    .toList();
            target.setPermissions(permissions);
        }
        // 递归转换子菜单
        if (source.getChildren() != null) {
            List<MenuPermission.MenuDTO> children = source.getChildren().stream()
                    .map(this::convertMenuDTO)
                    .toList();
            target.setChildren(children);
        }
        return target;
    }

    /**
     * 转换权限DTO
     */
    private MenuPermission.PermissionDTO convertPermissionDTO(MenuPermissionResponseDTO.PermissionDTO source) {
        MenuPermission.PermissionDTO target = new MenuPermission.PermissionDTO();
        target.setCode(source.getCode());
        target.setTitle(source.getTitle());
        target.setUrl(source.getUrl());
        return target;
    }
}
