package com.inboyu.admin.infrastructure.dao;

import com.inboyu.admin.domain.dept.model.Dept;
import com.inboyu.admin.infrastructure.dao.entity.DeptEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年07月29日 14:20
 */
public interface DeptDao extends JpaRepository<DeptEntity, Long> {

    /**
     * 根据组织id查询组织
     *
     * @param deptId
     * @param deleted
     * @return {@link DeptEntity }
     * <AUTHOR>
     * @date 2025/07/29 15:27:52
     */
    DeptEntity findByDeptIdAndDeleted(Long deptId, Integer deleted);

    /**
     * 根据上级组织id+组织名称查询组织数量
     *
     * @param parentDeptId
     * @param title
     * @param deleted
     * @return int
     * <AUTHOR>
     * @date 2025/07/29 20:32:27
     */
    int countByParentDeptIdAndTitleAndDeleted(Long parentDeptId, String title, Integer deleted);

    /**
     * 根据上级组织id+组织名称查询组织数量（排除指定组织）
     *
     * @param parentDeptId
     * @param title
     * @param deleted
     * @param deptId
     * @return int
     * <AUTHOR> Dong
     * @date 2025/07/31 09:55:00
     */
    int countByParentDeptIdAndTitleAndDeletedAndDeptIdNot(Long parentDeptId, String title, Integer deleted, Long deptId);

    /**
     * 根据是否删除标识查询
     *
     * @param deleted
     * @return {@link List }<{@link DeptEntity }>
     * <AUTHOR> Dong
     * @date 2025/07/30 14:34:53
     */
    List<DeptEntity> findByDeleted(int deleted);

    /**
     * 查询父组织下的所有子组织
     *
     * @param parentDeptId
     * @param deleted
     * @return {@link List }<{@link Dept }>
     * <AUTHOR> Dong
     * @date 2025/08/04 15:56:50
     */
    List<DeptEntity> findByParentDeptIdAndDeleted(Long parentDeptId, int deleted);

    /**
     * 删除部门（逻辑删除）
     *
     * @param deptId
     * @param deleted
     * @return int
     * <AUTHOR> Dong
     * @date 2025/08/04 15:56:50
     */
    @Transactional
    @Modifying
    @Query("update DeptEntity set deleted = :deleted where deptId = :deptId")
    int deleteByDeptId(Long deptId, Integer deleted);

    /**
     * 根据部门ID列表批量查询部门
     *
     * @param deptIds 部门ID列表
     * @param deleted 删除标识
     * @return {@link List }<{@link DeptEntity }>
     * <AUTHOR> Dong
     * @date 2025/08/05 12:00:00
     */
    List<DeptEntity> findByDeptIdInAndDeleted(List<Long> deptIds, Integer deleted);

    /**
     * 批量删除部门（逻辑删除）
     *
     * @param deptIds 部门ID列表
     * @param deleted 删除标识
     * @return int
     * <AUTHOR> Dong
     * @date 2025/08/05 12:00:00
     */
    @Transactional
    @Modifying
    @Query("update DeptEntity set deleted = :deleted, version = version + 1 where deptId in :deptIds")
    int deleteByDeptIdIn(List<Long> deptIds, Integer deleted);
}
