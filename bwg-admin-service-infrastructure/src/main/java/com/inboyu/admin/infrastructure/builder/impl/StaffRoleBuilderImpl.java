package com.inboyu.admin.infrastructure.builder.impl;

import com.inboyu.admin.domain.dept.model.DeptId;
import com.inboyu.admin.domain.role.model.RoleId;
import com.inboyu.admin.domain.staff.model.StaffDept;
import com.inboyu.admin.domain.staff.model.StaffId;
import com.inboyu.admin.domain.store.model.StoreId;
import com.inboyu.admin.infrastructure.builder.StaffRoleBuilder;
import com.inboyu.admin.infrastructure.dao.entity.StaffRoleEntity;
import com.inboyu.constant.DeleteFlag;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static java.util.stream.Collectors.joining;

/**
 * <AUTHOR> Dong
 * @date 2025年08月05日 17:09
 */
@Component
public class StaffRoleBuilderImpl implements StaffRoleBuilder {
    @Override
    public List<StaffRoleEntity> toEntities(List<StaffDept> staffDeptList) {
        return staffDeptList.stream().map(this::toEntity).toList();
    }

    @Override
    public StaffRoleEntity toEntity(StaffDept staffDept) {
        StaffRoleEntity entity = new StaffRoleEntity();
        entity.setStaffId(staffDept.getStaffId().getValue());
        entity.setDeptId(staffDept.getDeptId().getValue());
        entity.setIncludeAll(Boolean.TRUE.equals(staffDept.getIncludeAll()) ? 1 : 0);
        if (ObjectUtils.isNotEmpty(staffDept.getStoreIds())) {
            entity.setStoreIds(staffDept.getStoreIds().stream()
                    .map(storeId -> storeId.getValue().toString())
                    .collect(joining(",")));
        } else {
            entity.setStoreIds("");
        }
        if (ObjectUtils.isNotEmpty(staffDept.getRoleIds())) {
            entity.setRoleIds(staffDept.getRoleIds().stream()
                    .map(roleId -> roleId.getValue().toString())
                    .collect(joining(",")));
        } else {
            entity.setRoleIds("");
        }
        entity.setExpireTime(staffDept.getExpireTime());
        entity.setCreateTime(LocalDateTime.now());
        entity.setModifyTime(LocalDateTime.now());
        entity.setDeleted(DeleteFlag.NOT_DELETED);
        return entity;
    }

    @Override
    public List<StaffDept> toStaffDeptList(List<StaffRoleEntity> entityList) {
        return entityList.stream().map(this::toStaffDeptList).toList();
    }

    @Override
    public StaffDept toStaffDeptList(StaffRoleEntity staffRoleEntity) {
        String storeIdString = staffRoleEntity.getStoreIds();
        List<StoreId> storeIds = new ArrayList<>();
        if (StringUtils.isNotBlank(storeIdString)) {
            String[] strings = storeIdString.split(",");
            storeIds = Arrays.stream(strings).toList().stream().map(string -> StoreId.of(Long.valueOf(string))).toList();
        }
        String roleIdString = staffRoleEntity.getRoleIds();
        List<RoleId> roleIds = new ArrayList<>();
        if (StringUtils.isNotBlank(roleIdString)) {
            roleIds = Arrays.stream(roleIdString.split(",")).map(string -> RoleId.of(Long.valueOf(string))).toList();
        }
        return StaffDept
                .builder()
                .staffId(StaffId.of(staffRoleEntity.getStaffId()))
                .deptId(DeptId.of(staffRoleEntity.getDeptId()))
                .storeIds(storeIds)
                .roleIds(roleIds)
                .includeAll(1 == staffRoleEntity.getIncludeAll())
                .expireTime(staffRoleEntity.getExpireTime())
                .build();
    }

}
