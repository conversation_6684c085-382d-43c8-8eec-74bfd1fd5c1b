package com.inboyu.admin.infrastructure.builder.impl;

import com.inboyu.admin.domain.role.model.Permission;
import com.inboyu.admin.domain.role.model.RoleId;
import com.inboyu.admin.domain.role.model.RolePermission;
import com.inboyu.admin.infrastructure.builder.RoleScopeBuilder;
import com.inboyu.admin.infrastructure.dao.entity.RoleScopeEntity;
import com.inboyu.constant.DeleteFlag;
import com.inboyu.util.convert.BooleanIntegerConverter;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> Dong
 * @date 2025年07月31日 16:15
 */
@Component
public class RoleScopeBuilderImpl implements RoleScopeBuilder {
    @Override
    public List<RoleScopeEntity> toEntities(List<RolePermission> rolePermissions) {
        return rolePermissions.stream().map(item -> {
            RoleScopeEntity entity = new RoleScopeEntity();
            entity.setRoleId(item.getRoleId().getValue());
            entity.setGroupCode(item.getGroupCode());
            entity.setPerssion(item.getPermission().getValue());
            entity.setHidden(BooleanIntegerConverter.booleanToInt(item.getHidden()));
            entity.setDisabled(BooleanIntegerConverter.booleanToInt(item.getDisabled()));
            entity.setCreateTime(LocalDateTime.now());
            entity.setModifyTime(LocalDateTime.now());
            entity.setDeleted(DeleteFlag.NOT_DELETED);
            return entity;
        }).toList();
    }

    @Override
    public List<RolePermission> toRolePermission(List<RoleScopeEntity> roleScopeEntities) {
        return roleScopeEntities.stream()
                .map(entity -> RolePermission.create(RoleId.of(entity.getRoleId()), entity.getGroupCode(),
                        Permission.create(entity.getPerssion()),
                        BooleanIntegerConverter.intToBoolean(entity.getHidden()),
                        BooleanIntegerConverter.intToBoolean(entity.getDisabled()))).toList();
    }
}
